# 选项卡居中均匀分布实现说明

## 问题描述
用户希望让三个选项卡（单任务、多任务、设置）的标题在整个页面居中均匀分布。

## 解决方案

### 1. 样式配置优化 (ui_styles.py)

我们在 `ui_styles.py` 文件中对 `Modern.TNotebook.Tab` 样式进行了优化：

```python
# 配置选项卡样式以实现居中均匀分布
style.configure(
    'Modern.TNotebook.Tab',
    padding=[120, 15],  # 大的水平内边距让选项卡看起来均匀分布
    font=('Microsoft YaHei', 13, 'bold'),  # 稍大的字体更美观
    background='#e9ecef',
    foreground='#495057',
    anchor='center',   # 文本居中对齐
    justify='center'   # 文本对齐方式
)
```

### 2. 关键配置参数

- **padding=[120, 15]**: 大的水平内边距是实现均匀分布的关键
- **anchor='center'**: 确保文本在选项卡内居中对齐
- **justify='center'**: 文本对齐方式设为居中
- **font=('Microsoft YaHei', 13, 'bold')**: 使用稍大的字体提升视觉效果

### 3. 状态映射配置

```python
style.map(
    'Modern.TNotebook.Tab',
    background=[('selected', '#ffffff'), ('active', '#dee2e6')],
    foreground=[('selected', '#0d6efd'), ('active', '#495057')],
    padding=[('selected', [120, 15]), ('!selected', [120, 15])]  # 保持一致的内边距
)
```

### 4. 主界面配置 (calculator_ui.py)

在主界面中添加了配置方法：

```python
def configure_notebook_tabs(self):
    """配置选项卡样式以实现居中均匀分布"""
    try:
        # 确保选项卡样式已正确应用
        self.notebook.update_idletasks()
        
        # 可选：动态调整选项卡宽度
        tab_count = self.notebook.index("end")
        print(f"已配置 {tab_count} 个选项卡，使用均匀分布样式")
        
    except Exception as e:
        print(f"配置选项卡样式时出错: {e}")
```

## 实现原理

### 为什么使用大的padding？

在tkinter的ttk.Notebook中，选项卡的宽度主要由以下因素决定：
1. 文本内容的宽度
2. padding（内边距）
3. 字体大小

通过设置较大的水平padding（120像素），我们让每个选项卡都有足够的空间，从而在视觉上实现均匀分布的效果。

### 居中对齐的实现

- `anchor='center'`: 控制文本在选项卡内的位置
- `justify='center'`: 控制文本的对齐方式
- 这两个属性配合使用，确保选项卡标题文本完全居中

## 效果验证

程序运行后会显示：
```
已配置 3 个选项卡，使用均匀分布样式
```

这表明配置已成功应用。

## 演示程序

我们还创建了一个独立的演示程序 `notebook_style_demo.py`，专门展示选项卡居中均匀分布的效果。

## 注意事项

1. **padding值的调整**: 如果窗口宽度发生变化，可能需要调整padding值以保持最佳效果
2. **字体大小**: 较大的字体配合适当的padding能获得更好的视觉效果
3. **兼容性**: 这种方法在不同操作系统和主题下都能保持良好的效果

## 总结

通过合理配置ttk.Notebook的样式，特别是使用大的水平padding和居中对齐设置，我们成功实现了选项卡标题在整个页面的居中均匀分布效果。这种方法简单有效，且不需要复杂的布局计算。
