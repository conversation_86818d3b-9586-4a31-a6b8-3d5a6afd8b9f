#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置方法是否被调用
"""

import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from calculator_ui import CeramicCalculatorUI
import time

def test_config():
    print("🚀 开始测试配置方法调用...")
    
    # 创建应用
    app = CeramicCalculatorUI()
    print("✅ 应用创建完成")
    
    # 等待延迟配置执行
    print("⏳ 等待延迟配置执行...")
    time.sleep(1)
    
    # 手动调用配置方法
    print("🔧 手动调用配置方法...")
    app.configure_notebook_tabs()
    
    # 测试窗口大小变化
    print("📏 测试窗口大小变化...")
    app.window.geometry("800x600")
    app.window.update_idletasks()
    time.sleep(0.5)
    
    app.window.geometry("1200x800")
    app.window.update_idletasks()
    time.sleep(0.5)
    
    # 再次手动调用
    print("🔧 再次手动调用配置方法...")
    app.configure_notebook_tabs()
    
    print("✅ 测试完成")

if __name__ == "__main__":
    test_config()
