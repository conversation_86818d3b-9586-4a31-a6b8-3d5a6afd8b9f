#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极修复方案 - 直接替换主程序中的配置方法
"""

import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from calculator_ui import CeramicCalculatorUI
import ttkbootstrap as ttk

def fixed_configure_notebook_tabs(self):
    """修复的配置选项卡方法"""
    try:
        print("\n" + "="*50)
        print("🔧 开始终极修复配置...")
        
        # 强制更新所有组件
        self.window.update_idletasks()
        self.content_container.update_idletasks()
        self.notebook.update_idletasks()
        
        # 获取尺寸信息
        window_width = self.window.winfo_width()
        container_width = self.content_container.winfo_width()
        tab_count = self.notebook.index("end")
        
        print(f"📏 窗口宽度: {window_width}px")
        print(f"📐 容器宽度: {container_width}px")
        print(f"📊 选项卡数量: {tab_count}")
        
        if container_width <= 1:
            container_width = window_width - 40
            print(f"⚠️  使用估算容器宽度: {container_width}px")
        
        # 计算真正的1/3宽度
        ideal_tab_width = container_width / tab_count
        target_tab_width = int(ideal_tab_width)
        
        print(f"🎯 目标选项卡宽度: {target_tab_width}px ({target_tab_width/container_width*100:.1f}%)")
        
        # 文本宽度计算
        tab_names = ["单任务", "多任务", "设置"]
        max_chars = max(len(name) for name in tab_names)
        
        # 根据窗口大小调整字体
        if container_width < 500:
            font_size = 10
            char_width = 11
        elif container_width < 800:
            font_size = 11
            char_width = 12
        else:
            font_size = 12
            char_width = 13
        
        text_width = max_chars * char_width + 16
        print(f"📝 文本宽度: {text_width}px (字体: {font_size}px)")
        
        # 关键修复：计算实现1/3宽度所需的padding
        total_padding_needed = target_tab_width - text_width
        padding_per_side = total_padding_needed // 2
        
        print(f"💡 关键计算:")
        print(f"   - 目标宽度: {target_tab_width}px")
        print(f"   - 文本宽度: {text_width}px")
        print(f"   - 需要总padding: {total_padding_needed}px")
        print(f"   - 每边padding: {padding_per_side}px")
        
        # 设置合理的padding范围
        if total_padding_needed <= 0:
            padding = 1
            print(f"⚠️  空间不足，使用最小padding: {padding}px")
        else:
            # 根据窗口大小设置最大padding限制
            if container_width < 500:
                max_padding = 30
            elif container_width < 800:
                max_padding = 80
            elif container_width < 1200:
                max_padding = 150
            else:
                max_padding = 400  # 大窗口允许更大的padding
            
            padding = max(2, min(max_padding, padding_per_side))
            print(f"✅ 计算padding: {padding}px (限制: 2-{max_padding}px)")
        
        # 应用样式
        style = ttk.Style()
        style.configure(
            'Modern.TNotebook.Tab',
            padding=[padding, 10],
            font=('Microsoft YaHei', font_size, 'bold'),
            background='#e9ecef',
            foreground='#495057',
            anchor='center'
        )
        
        style.map(
            'Modern.TNotebook.Tab',
            background=[('selected', '#ffffff'), ('active', '#dee2e6')],
            foreground=[('selected', '#0d6efd'), ('active', '#495057')],
            padding=[('selected', [padding, 10]), ('!selected', [padding, 10])]
        )
        
        # 强制刷新
        self.notebook.update_idletasks()
        
        # 计算最终结果
        final_width = text_width + padding * 2
        final_percentage = (final_width / container_width * 100) if container_width > 0 else 0
        target_percentage = 100 / tab_count
        deviation = abs(final_percentage - target_percentage)
        
        print(f"✅ 最终结果:")
        print(f"   - 实际宽度: {final_width}px ({final_percentage:.1f}%)")
        print(f"   - 目标百分比: {target_percentage:.1f}%")
        print(f"   - 偏差: {deviation:.1f}%")
        
        if deviation <= 3:
            print(f"🎉 成功实现1/3宽度分布！")
        elif final_width > target_tab_width:
            print(f"⚠️  可能有文字遮挡")
        else:
            print(f"⚠️  偏离目标较多")
            
    except Exception as e:
        print(f"❌ 配置出错: {e}")
        import traceback
        traceback.print_exc()

def test_ultimate_fix():
    """测试终极修复方案"""
    print("🚀 启动终极修复测试...")
    
    # 创建应用
    app = CeramicCalculatorUI()
    
    # 替换配置方法
    app.configure_notebook_tabs = lambda: fixed_configure_notebook_tabs(app)
    
    print("✅ 应用创建完成，配置方法已替换")
    
    # 测试不同窗口大小
    test_sizes = [
        (400, 300, "超小窗口"),
        (600, 400, "小窗口"),
        (800, 600, "中等窗口"),
        (1200, 800, "大窗口"),
        (1600, 1000, "超大窗口")
    ]
    
    for width, height, desc in test_sizes:
        print(f"\n🔍 测试 {desc} ({width}x{height}):")
        app.window.geometry(f"{width}x{height}")
        app.window.update_idletasks()
        app.configure_notebook_tabs()
    
    print("\n🎯 启动应用界面进行最终测试...")
    app.run()

if __name__ == "__main__":
    test_ultimate_fix()
