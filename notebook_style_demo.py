#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示选项卡居中均匀分布的最终版本
"""

import ttkbootstrap as ttk
from ttkbootstrap import Style

def create_demo_window():
    """创建演示窗口"""
    # 创建主窗口
    window = ttk.Window(themename="cosmo")
    window.title("选项卡居中均匀分布演示")
    window.geometry("900x700")
    
    # 配置样式
    style = Style()
    
    # 配置现代化的Notebook样式 - 实现选项卡居中均匀分布
    style.configure(
        'Centered.TNotebook',
        background='#f8f9fa',
        borderwidth=0,
        tabposition='n'
    )
    
    # 关键配置：使用大的padding实现均匀分布效果
    style.configure(
        'Centered.TNotebook.Tab',
        padding=[120, 15],  # 大的水平内边距让选项卡看起来均匀分布
        font=('Microsoft YaHei', 13, 'bold'),
        background='#e9ecef',
        foreground='#495057',
        anchor='center',  # 文本居中
        justify='center'  # 文本对齐方式
    )
    
    # 配置选项卡的状态映射
    style.map(
        'Centered.TNotebook.Tab',
        background=[('selected', '#ffffff'), ('active', '#dee2e6')],
        foreground=[('selected', '#0d6efd'), ('active', '#495057')],
        padding=[('selected', [120, 15]), ('!selected', [120, 15])]
    )
    
    # 创建主容器
    main_container = ttk.Frame(window)
    main_container.pack(fill="both", expand=True, padx=30, pady=30)
    main_container.grid_rowconfigure(0, weight=1)
    main_container.grid_columnconfigure(0, weight=1)
    
    # 标题
    title_label = ttk.Label(
        main_container, 
        text="选项卡居中均匀分布演示", 
        font=('Microsoft YaHei', 16, 'bold'),
        foreground='#0d6efd'
    )
    title_label.pack(pady=(0, 20))
    
    # 创建选项卡控件
    notebook = ttk.Notebook(main_container, style='Centered.TNotebook', width=840)
    notebook.pack(fill="both", expand=True)
    
    # 创建三个选项卡
    tab1 = ttk.Frame(notebook)
    notebook.add(tab1, text="单任务")
    
    tab2 = ttk.Frame(notebook)
    notebook.add(tab2, text="多任务")
    
    tab3 = ttk.Frame(notebook)
    notebook.add(tab3, text="设置")
    
    # 在每个选项卡中添加内容
    create_tab_content(tab1, "单任务", "这里是单任务计算界面\n可以输入单个化学式进行计算")
    create_tab_content(tab2, "多任务", "这里是多任务批量计算界面\n可以批量处理多个化学式")
    create_tab_content(tab3, "设置", "这里是设置界面\n可以管理原料数据和系统配置")
    
    return window

def create_tab_content(parent, title, description):
    """创建选项卡内容"""
    # 主容器
    content_frame = ttk.Frame(parent)
    content_frame.pack(fill="both", expand=True, padx=40, pady=40)
    
    # 标题
    title_label = ttk.Label(
        content_frame, 
        text=title, 
        font=('Microsoft YaHei', 18, 'bold'),
        foreground='#0d6efd'
    )
    title_label.pack(pady=(0, 20))
    
    # 描述
    desc_label = ttk.Label(
        content_frame, 
        text=description, 
        font=('Microsoft YaHei', 12),
        foreground='#495057',
        justify='center'
    )
    desc_label.pack(pady=(0, 30))
    
    # 示例按钮
    button_frame = ttk.Frame(content_frame)
    button_frame.pack()
    
    ttk.Button(
        button_frame, 
        text=f"执行{title}", 
        bootstyle="primary",
        width=15
    ).pack(side="left", padx=10)
    
    ttk.Button(
        button_frame, 
        text="查看帮助", 
        bootstyle="info-outline",
        width=15
    ).pack(side="left", padx=10)

if __name__ == "__main__":
    window = create_demo_window()
    print("演示程序启动成功！")
    print("选项卡标题应该在整个页面居中均匀分布")
    window.mainloop()
