#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示选项卡自适应宽度（1/3页面宽度）的程序
"""

import ttkbootstrap as ttk
from ttkbootstrap import Style

class AdaptiveNotebookDemo:
    def __init__(self):
        # 创建主窗口
        self.window = ttk.Window(themename="cosmo")
        self.window.title("选项卡自适应宽度演示")
        self.window.geometry("1200x800")
        
        # 配置样式
        self.style = Style()
        self.configure_initial_styles()
        
        # 创建界面
        self.create_widgets()
        
        # 绑定窗口大小变化事件
        self.window.bind('<Configure>', self.on_window_resize)
        
    def configure_initial_styles(self):
        """配置初始样式"""
        self.style.configure(
            'Adaptive.TNotebook',
            background='#f8f9fa',
            borderwidth=0,
            tabposition='n'
        )
        
        # 初始样式配置
        self.style.configure(
            'Adaptive.TNotebook.Tab',
            padding=[50, 15],
            font=('Microsoft YaHei', 13, 'bold'),
            background='#e9ecef',
            foreground='#495057',
            anchor='center',
            justify='center'
        )
        
        self.style.map(
            'Adaptive.TNotebook.Tab',
            background=[('selected', '#ffffff'), ('active', '#dee2e6')],
            foreground=[('selected', '#0d6efd'), ('active', '#495057')]
        )
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_container = ttk.Frame(self.window)
        main_container.pack(fill="both", expand=True, padx=20, pady=20)
        main_container.grid_rowconfigure(1, weight=1)
        main_container.grid_columnconfigure(0, weight=1)
        
        # 标题和说明
        title_frame = ttk.Frame(main_container)
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 20))
        
        ttk.Label(
            title_frame, 
            text="选项卡自适应宽度演示", 
            font=('Microsoft YaHei', 16, 'bold'),
            foreground='#0d6efd'
        ).pack()
        
        ttk.Label(
            title_frame, 
            text="每个选项卡宽度自动调整为页面宽度的1/3 - 尝试调整窗口大小", 
            font=('Microsoft YaHei', 11),
            foreground='#6c757d'
        ).pack(pady=(5, 0))
        
        # 内容容器
        self.content_container = ttk.Frame(main_container)
        self.content_container.grid(row=1, column=0, sticky="nsew")
        self.content_container.grid_rowconfigure(0, weight=1)
        self.content_container.grid_columnconfigure(0, weight=1)
        
        # 创建选项卡控件
        self.notebook = ttk.Notebook(self.content_container, style='Adaptive.TNotebook')
        self.notebook.grid(row=0, column=0, sticky="nsew")
        
        # 创建三个选项卡
        self.create_tabs()
        
        # 初始配置选项卡宽度
        self.window.after(100, self.configure_adaptive_tabs)
        
    def create_tabs(self):
        """创建选项卡内容"""
        # 单任务选项卡
        tab1 = ttk.Frame(self.notebook)
        self.notebook.add(tab1, text="单任务")
        self.create_tab_content(tab1, "单任务计算", "这里是单任务计算界面")
        
        # 多任务选项卡
        tab2 = ttk.Frame(self.notebook)
        self.notebook.add(tab2, text="多任务")
        self.create_tab_content(tab2, "多任务计算", "这里是多任务批量计算界面")
        
        # 设置选项卡
        tab3 = ttk.Frame(self.notebook)
        self.notebook.add(tab3, text="设置")
        self.create_tab_content(tab3, "系统设置", "这里是系统设置界面")
        
    def create_tab_content(self, parent, title, description):
        """创建选项卡内容"""
        content_frame = ttk.Frame(parent)
        content_frame.pack(fill="both", expand=True, padx=40, pady=40)
        
        ttk.Label(
            content_frame, 
            text=title, 
            font=('Microsoft YaHei', 18, 'bold'),
            foreground='#0d6efd'
        ).pack(pady=(0, 20))
        
        ttk.Label(
            content_frame, 
            text=description, 
            font=('Microsoft YaHei', 12),
            foreground='#495057'
        ).pack(pady=(0, 30))
        
        # 显示当前窗口信息
        self.info_label = ttk.Label(
            content_frame, 
            text="窗口信息将在这里显示", 
            font=('Microsoft YaHei', 10),
            foreground='#6c757d'
        )
        self.info_label.pack()
        
    def configure_adaptive_tabs(self):
        """配置自适应选项卡宽度"""
        try:
            # 确保组件已渲染
            self.notebook.update_idletasks()
            self.content_container.update_idletasks()
            
            # 获取选项卡数量
            tab_count = self.notebook.index("end")
            
            # 获取容器宽度
            container_width = self.content_container.winfo_width()
            if container_width <= 1:
                window_width = self.window.winfo_width()
                container_width = window_width - 40  # 减去边距
            
            # 计算每个选项卡的目标宽度（1/3页面宽度）
            target_tab_width = container_width // tab_count if tab_count > 0 else container_width // 3
            
            # 估算文本宽度并计算padding
            text_width_estimate = 80  # 根据字体和文本长度估算
            padding_horizontal = max(20, (target_tab_width - text_width_estimate) // 2)
            
            # 更新样式
            self.style.configure(
                'Adaptive.TNotebook.Tab',
                padding=[padding_horizontal, 15],
                font=('Microsoft YaHei', 13, 'bold'),
                background='#e9ecef',
                foreground='#495057',
                anchor='center',
                justify='center'
            )
            
            self.style.map(
                'Adaptive.TNotebook.Tab',
                background=[('selected', '#ffffff'), ('active', '#dee2e6')],
                foreground=[('selected', '#0d6efd'), ('active', '#495057')],
                padding=[('selected', [padding_horizontal, 15]), ('!selected', [padding_horizontal, 15])]
            )
            
            # 更新信息显示
            info_text = f"窗口宽度: {self.window.winfo_width()}px\n容器宽度: {container_width}px\n每个选项卡目标宽度: {target_tab_width}px\nPadding: {padding_horizontal}px"
            if hasattr(self, 'info_label'):
                self.info_label.config(text=info_text)
            
            print(f"自适应配置: 容器宽度={container_width}px, 选项卡宽度={target_tab_width}px, padding={padding_horizontal}px")
            
        except Exception as e:
            print(f"配置自适应选项卡时出错: {e}")
    
    def on_window_resize(self, event):
        """窗口大小变化事件处理"""
        if event.widget == self.window:
            # 延迟执行，确保窗口大小变化完成
            self.window.after(100, self.configure_adaptive_tabs)
    
    def run(self):
        """运行程序"""
        self.window.mainloop()

if __name__ == "__main__":
    app = AdaptiveNotebookDemo()
    print("自适应选项卡演示程序启动！")
    print("尝试调整窗口大小，观察选项卡宽度的变化")
    app.run()
