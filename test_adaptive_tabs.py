#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试选项卡自适应宽度功能
"""

import ttkbootstrap as ttk
from ttkbootstrap import Style

class TabWidthTester:
    def __init__(self):
        self.window = ttk.Window(themename="cosmo")
        self.window.title("选项卡宽度测试器")
        self.window.geometry("1000x700")
        
        self.style = Style()
        self.setup_styles()
        self.create_interface()
        
        # 绑定事件
        self.window.bind('<Configure>', self.on_resize)
        
        # 延迟初始配置
        self.window.after(100, self.update_tab_widths)
        
    def setup_styles(self):
        """设置样式"""
        self.style.configure(
            'Test.TNotebook',
            background='#f8f9fa',
            borderwidth=1
        )
        
        self.style.configure(
            'Test.TNotebook.Tab',
            padding=[50, 12],
            font=('Microsoft YaHei', 12, 'bold'),
            background='#e9ecef',
            foreground='#495057',
            anchor='center'
        )
        
        self.style.map(
            'Test.TNotebook.Tab',
            background=[('selected', '#ffffff'), ('active', '#dee2e6')],
            foreground=[('selected', '#0d6efd'), ('active', '#495057')]
        )
    
    def create_interface(self):
        """创建界面"""
        # 主容器
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(
            main_frame,
            text="选项卡自适应宽度测试",
            font=('Microsoft YaHei', 16, 'bold'),
            foreground='#0d6efd'
        )
        title_label.pack(pady=(0, 10))
        
        # 信息显示区域
        self.info_frame = ttk.LabelFrame(main_frame, text="实时信息", padding=10)
        self.info_frame.pack(fill="x", pady=(0, 20))
        
        self.info_text = ttk.Label(
            self.info_frame,
            text="信息加载中...",
            font=('Consolas', 10),
            foreground='#495057'
        )
        self.info_text.pack(anchor="w")
        
        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill="x", pady=(0, 20))
        
        ttk.Button(
            control_frame,
            text="小窗口 (800x600)",
            command=lambda: self.set_window_size(800, 600),
            bootstyle="primary"
        ).pack(side="left", padx=(0, 10))
        
        ttk.Button(
            control_frame,
            text="中等窗口 (1200x800)",
            command=lambda: self.set_window_size(1200, 800),
            bootstyle="info"
        ).pack(side="left", padx=(0, 10))
        
        ttk.Button(
            control_frame,
            text="大窗口 (1600x1000)",
            command=lambda: self.set_window_size(1600, 1000),
            bootstyle="success"
        ).pack(side="left", padx=(0, 10))
        
        ttk.Button(
            control_frame,
            text="刷新计算",
            command=self.update_tab_widths,
            bootstyle="warning"
        ).pack(side="right")
        
        # 选项卡容器
        self.notebook_container = ttk.Frame(main_frame)
        self.notebook_container.pack(fill="both", expand=True)
        self.notebook_container.grid_rowconfigure(0, weight=1)
        self.notebook_container.grid_columnconfigure(0, weight=1)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(self.notebook_container, style='Test.TNotebook')
        self.notebook.grid(row=0, column=0, sticky="nsew")
        
        # 添加选项卡
        for i, name in enumerate(["单任务", "多任务", "设置"]):
            tab = ttk.Frame(self.notebook)
            self.notebook.add(tab, text=name)
            
            # 添加选项卡内容
            content_label = ttk.Label(
                tab,
                text=f"这是 {name} 选项卡\n\n当前选项卡应该占用页面宽度的 1/3",
                font=('Microsoft YaHei', 14),
                foreground='#495057',
                justify='center'
            )
            content_label.pack(expand=True)
    
    def set_window_size(self, width, height):
        """设置窗口大小"""
        self.window.geometry(f"{width}x{height}")
        # 延迟更新，等待窗口大小变化完成
        self.window.after(200, self.update_tab_widths)
    
    def update_tab_widths(self):
        """更新选项卡宽度"""
        try:
            # 确保组件已渲染
            self.notebook.update_idletasks()
            self.notebook_container.update_idletasks()
            
            # 获取尺寸信息
            window_width = self.window.winfo_width()
            window_height = self.window.winfo_height()
            container_width = self.notebook_container.winfo_width()
            tab_count = self.notebook.index("end")
            
            # 计算目标宽度
            target_tab_width = container_width // tab_count if tab_count > 0 else 0
            
            # 计算文本宽度
            max_text_length = max(len("单任务"), len("多任务"), len("设置"))
            text_width_estimate = max_text_length * 15 + 20
            
            # 计算padding
            padding_horizontal = max(20, min(300, (target_tab_width - text_width_estimate) // 2))
            
            # 更新样式
            self.style.configure(
                'Test.TNotebook.Tab',
                padding=[padding_horizontal, 12],
                font=('Microsoft YaHei', 12, 'bold'),
                background='#e9ecef',
                foreground='#495057',
                anchor='center'
            )
            
            self.style.map(
                'Test.TNotebook.Tab',
                background=[('selected', '#ffffff'), ('active', '#dee2e6')],
                foreground=[('selected', '#0d6efd'), ('active', '#495057')],
                padding=[('selected', [padding_horizontal, 12]), ('!selected', [padding_horizontal, 12])]
            )
            
            # 更新信息显示
            info_text = f"""窗口尺寸: {window_width} x {window_height} px
容器宽度: {container_width} px
选项卡数量: {tab_count}
每个选项卡目标宽度: {target_tab_width} px ({target_tab_width/container_width*100:.1f}% 容器宽度)
文本宽度估算: {text_width_estimate} px
计算的padding: {padding_horizontal} px
实际选项卡宽度: {text_width_estimate + padding_horizontal * 2} px"""
            
            self.info_text.config(text=info_text)
            
            print(f"更新选项卡宽度: 容器={container_width}px, 目标宽度={target_tab_width}px, padding={padding_horizontal}px")
            
        except Exception as e:
            print(f"更新选项卡宽度时出错: {e}")
            self.info_text.config(text=f"错误: {e}")
    
    def on_resize(self, event):
        """窗口大小变化事件"""
        if event.widget == self.window:
            self.window.after(100, self.update_tab_widths)
    
    def run(self):
        """运行程序"""
        self.window.mainloop()

if __name__ == "__main__":
    app = TabWidthTester()
    print("选项卡宽度测试器启动！")
    print("尝试点击按钮改变窗口大小，或手动调整窗口大小")
    app.run()
