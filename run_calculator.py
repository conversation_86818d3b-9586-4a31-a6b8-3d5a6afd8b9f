#!/usr/bin/env python3
"""
储能陶瓷配料计算器启动脚本
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from calculator_ui import CeramicCalculatorUI
    
    def main():
        """主函数"""
        print("正在启动储能陶瓷配料计算器...")
        
        # 创建并运行应用程序
        app = CeramicCalculatorUI()
        app.run()
        
        print("程序已退出")

    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保所有依赖包已正确安装:")
    print("- ttkbootstrap")
    print("- pandas")
    print("- openpyxl")
    input("按回车键退出...")
    
except Exception as e:
    print(f"程序运行错误: {e}")
    input("按回车键退出...")
