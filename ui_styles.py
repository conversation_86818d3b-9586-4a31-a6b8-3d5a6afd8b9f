"""
UI样式配置文件
为陶瓷配料计算器提供现代化的样式配置
"""

import ttkbootstrap as ttk
from ttkbootstrap import Style


def configure_styles(window):
    """配置应用程序样式"""
    style = Style()
    
    # 配置现代化的LabelFrame样式
    style.configure(
        'Modern.TLabelframe',
        borderwidth=2,
        relief='solid',
        bordercolor='#e9ecef',
        background='#ffffff'
    )
    
    style.configure(
        'Modern.TLabelframe.Label',
        background='#ffffff',
        foreground='#495057',
        font=('Microsoft YaHei', 12, 'bold')
    )
    
    # 配置现代化的Notebook样式 - 实现选项卡居中均匀分布
    style.configure(
        'Modern.TNotebook',
        background='#f8f9fa',
        borderwidth=0,
        tabposition='n'  # 确保选项卡在顶部
    )

    # 配置选项卡样式以实现居中均匀分布
    # 关键：使用大的padding实现视觉上的均匀分布效果
    style.configure(
        'Modern.TNotebook.Tab',
        padding=[120, 15],  # 大的水平内边距让选项卡看起来均匀分布
        font=('Microsoft YaHei', 13, 'bold'),  # 稍大的字体更美观
        background='#e9ecef',
        foreground='#495057',
        anchor='center',   # 文本居中对齐
        justify='center'   # 文本对齐方式
    )

    # 配置选项卡的状态映射
    style.map(
        'Modern.TNotebook.Tab',
        background=[('selected', '#ffffff'), ('active', '#dee2e6')],
        foreground=[('selected', '#0d6efd'), ('active', '#495057')],
        padding=[('selected', [120, 15]), ('!selected', [120, 15])]  # 保持一致的内边距
    )
    
    # 配置大按钮样式
    style.configure(
        'Large.TButton',
        font=('Microsoft YaHei', 12, 'bold'),
        padding=[15, 10]
    )
    
    # 配置Header样式
    style.configure(
        'Header.TFrame',
        background='#ffffff',
        relief='solid',
        borderwidth=1,
        bordercolor='#dee2e6'
    )
    
    return style


def get_color_scheme():
    """获取颜色方案"""
    return {
        'primary': '#0d6efd',
        'secondary': '#6c757d',
        'success': '#198754',
        'info': '#0dcaf0',
        'warning': '#ffc107',
        'danger': '#dc3545',
        'light': '#f8f9fa',
        'dark': '#212529',
        'white': '#ffffff',
        'border': '#dee2e6',
        'text_primary': '#212529',
        'text_secondary': '#6c757d',
        'text_muted': '#adb5bd'
    }


def get_font_scheme():
    """获取字体方案"""
    return {
        'title': ('Microsoft YaHei', 24, 'bold'),
        'subtitle': ('Microsoft YaHei', 14),
        'heading': ('Microsoft YaHei', 16, 'bold'),
        'subheading': ('Microsoft YaHei', 14, 'bold'),
        'body': ('Microsoft YaHei', 12),
        'small': ('Microsoft YaHei', 10),
        'button': ('Microsoft YaHei', 11, 'bold'),
        'code': ('Consolas', 11)
    }


def apply_modern_theme(window):
    """应用现代化主题"""
    # 设置窗口背景
    window.configure(bg='#f8f9fa')
    
    # 配置样式
    style = configure_styles(window)
    
    return style, get_color_scheme(), get_font_scheme()
