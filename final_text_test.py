#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试：解决选项卡文字被遮挡的问题
"""

import ttkbootstrap as ttk
from ttkbootstrap import Style

class FinalTextTest:
    def __init__(self):
        self.window = ttk.Window(themename="cosmo")
        self.window.title("最终文字显示测试")
        self.window.geometry("400x300")  # 从很小的窗口开始
        
        self.style = Style()
        self.create_interface()
        
        # 绑定事件
        self.window.bind('<Configure>', self.on_resize)
        
        # 延迟初始配置
        self.window.after(100, self.update_tabs)
        
    def create_interface(self):
        """创建界面"""
        # 主容器
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 标题
        ttk.Label(
            main_frame,
            text="文字遮挡问题测试",
            font=('Microsoft YaHei', 12, 'bold'),
            foreground='#0d6efd'
        ).pack(pady=(0, 5))
        
        # 控制按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill="x", pady=(0, 5))
        
        ttk.Button(btn_frame, text="350px", command=lambda: self.resize(350, 250), width=8).pack(side="left", padx=1)
        ttk.Button(btn_frame, text="400px", command=lambda: self.resize(400, 300), width=8).pack(side="left", padx=1)
        ttk.Button(btn_frame, text="500px", command=lambda: self.resize(500, 400), width=8).pack(side="left", padx=1)
        ttk.Button(btn_frame, text="800px", command=lambda: self.resize(800, 600), width=8).pack(side="left", padx=1)
        
        # 信息显示
        self.info_label = ttk.Label(
            main_frame,
            text="信息加载中...",
            font=('Consolas', 8),
            foreground='#6c757d'
        )
        self.info_label.pack(pady=(0, 5))
        
        # 选项卡容器
        self.notebook_container = ttk.Frame(main_frame)
        self.notebook_container.pack(fill="both", expand=True)
        self.notebook_container.grid_rowconfigure(0, weight=1)
        self.notebook_container.grid_columnconfigure(0, weight=1)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(self.notebook_container)
        self.notebook.grid(row=0, column=0, sticky="nsew")
        
        # 添加选项卡
        for name in ["单任务", "多任务", "设置"]:
            tab = ttk.Frame(self.notebook)
            self.notebook.add(tab, text=name)
            
            ttk.Label(
                tab,
                text=f"{name}\n选项卡",
                font=('Microsoft YaHei', 11),
                foreground='#495057',
                justify='center'
            ).pack(expand=True)
    
    def resize(self, width, height):
        """调整窗口大小"""
        self.window.geometry(f"{width}x{height}")
        self.window.after(100, self.update_tabs)
    
    def update_tabs(self):
        """更新选项卡样式 - 解决文字遮挡问题"""
        try:
            # 确保组件已渲染
            self.notebook.update_idletasks()
            self.notebook_container.update_idletasks()
            
            # 获取尺寸
            window_width = self.window.winfo_width()
            container_width = self.notebook_container.winfo_width()
            tab_count = self.notebook.index("end")
            
            if container_width <= 1:
                container_width = window_width - 10
            
            # 计算目标宽度
            target_width = container_width // tab_count if tab_count > 0 else 100
            
            # 文本宽度估算（保守估计）
            text_width = 3 * 18 + 20  # 3个字符，每个18px，加20px边距
            
            # 计算安全的padding
            available = target_width - text_width
            
            if container_width < 400:  # 超小窗口
                padding = max(1, min(5, available // 2))
                font_size = 10
            elif container_width < 600:  # 小窗口
                padding = max(3, min(15, available // 2))
                font_size = 11
            elif container_width < 900:  # 中等窗口
                padding = max(8, min(40, available // 2))
                font_size = 12
            else:  # 大窗口
                padding = max(15, min(100, available // 2))
                font_size = 13
            
            # 最终安全检查
            if available <= 0:
                padding = 1  # 最小可能的padding
            
            # 配置样式
            self.style.configure(
                'TNotebook.Tab',
                padding=[padding, 8],
                font=('Microsoft YaHei', font_size, 'bold'),
                background='#e9ecef',
                foreground='#495057',
                anchor='center'
            )
            
            self.style.map(
                'TNotebook.Tab',
                background=[('selected', '#ffffff'), ('active', '#dee2e6')],
                foreground=[('selected', '#0d6efd'), ('active', '#495057')],
                padding=[('selected', [padding, 8]), ('!selected', [padding, 8])]
            )
            
            # 强制更新
            self.notebook.update_idletasks()
            
            # 计算实际宽度
            actual_width = text_width + padding * 2
            
            # 更新信息显示
            status = "✅ 正常" if actual_width <= target_width else "⚠️ 可能遮挡"
            info_text = f"窗口: {window_width}px | 容器: {container_width}px\n"
            info_text += f"目标: {target_width}px | 实际: {actual_width}px\n"
            info_text += f"Padding: {padding}px | 字体: {font_size}px | {status}"
            
            self.info_label.config(text=info_text)
            
            print(f"更新: 容器={container_width}px, 目标={target_width}px, 实际={actual_width}px, padding={padding}px, 状态={status}")
            
        except Exception as e:
            print(f"更新出错: {e}")
            self.info_label.config(text=f"错误: {e}")
    
    def on_resize(self, event):
        """窗口大小变化事件"""
        if event.widget == self.window:
            self.window.after(50, self.update_tabs)
    
    def run(self):
        """运行程序"""
        print("最终文字显示测试启动！")
        print("请点击按钮或手动调整窗口大小，观察选项卡文字是否完整显示")
        self.window.mainloop()

if __name__ == "__main__":
    app = FinalTextTest()
    app.run()
