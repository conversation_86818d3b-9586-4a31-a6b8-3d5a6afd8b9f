def configure_notebook_tabs(self):
    """配置选项卡样式 - 修复版本"""
    try:
        print("\n" + "="*50)
        print("🔧 开始终极选项卡配置...")
        
        # 强制更新所有组件，确保获取准确尺寸
        self.window.update_idletasks()
        self.content_container.update_idletasks()
        self.notebook.update_idletasks()
        
        # 获取精确尺寸信息
        window_width = self.window.winfo_width()
        container_width = self.content_container.winfo_width()
        tab_count = self.notebook.index("end")
        
        print(f"📏 窗口宽度: {window_width}px")
        print(f"📐 容器宽度: {container_width}px")
        print(f"📊 选项卡数量: {tab_count}")
        
        # 容器宽度验证和修正
        if container_width <= 1:
            container_width = window_width - 40  # 减去边距
            print(f"⚠️  使用估算容器宽度: {container_width}px")
        
        # 计算真正的1/3宽度（精确到小数）
        ideal_tab_width = container_width / tab_count if tab_count > 0 else container_width / 3
        target_tab_width = int(ideal_tab_width)
        
        print(f"🎯 理想选项卡宽度: {ideal_tab_width:.1f}px")
        print(f"🎯 目标选项卡宽度: {target_tab_width}px ({target_tab_width/container_width*100:.1f}%)")
        
        # 精确计算文本宽度 - 使用保守估算确保文字不被遮挡
        tab_names = ["单任务", "多任务", "设置"]
        max_chars = max(len(name) for name in tab_names)
        
        # 根据窗口大小动态调整字体和字符宽度估算
        if container_width < 500:
            font_size = 10
            char_width = 11
        elif container_width < 800:
            font_size = 11
            char_width = 12
        else:
            font_size = 12
            char_width = 13
        
        text_base_width = max_chars * char_width
        text_padding = 16  # 文本内部边距
        min_text_width = text_base_width + text_padding
        
        print(f"📝 文本宽度计算:")
        print(f"   - 最大字符数: {max_chars}")
        print(f"   - 字体大小: {font_size}px")
        print(f"   - 字符宽度: {char_width}px")
        print(f"   - 最小文本宽度: {min_text_width}px")
        
        # 计算实现1/3宽度所需的总padding
        required_total_padding = target_tab_width - min_text_width
        required_padding_per_side = required_total_padding // 2
        
        print(f"💾 实现1/3宽度需要:")
        print(f"   - 总padding: {required_total_padding}px")
        print(f"   - 每边padding: {required_padding_per_side}px")
        
        # 智能padding计算 - 目标是实现真正的1/3宽度
        if required_total_padding <= 0:
            # 空间严重不足，使用最小配置
            padding = 1
            font_size = max(9, font_size - 1)  # 减小字体
            print(f"⚠️  空间严重不足，使用最小配置: padding={padding}px, font={font_size}px")
        elif required_total_padding < 6:
            # 空间不足，使用紧凑配置
            padding = max(1, required_padding_per_side)
            print(f"⚠️  空间不足，使用紧凑配置: padding={padding}px")
        else:
            # 有足够空间，根据窗口大小设置合理的padding范围
            if container_width < 500:
                max_allowed_padding = 50
                min_allowed_padding = 2
                mode = "超小窗口"
            elif container_width < 800:
                max_allowed_padding = 100
                min_allowed_padding = 5
                mode = "小窗口"
            elif container_width < 1200:
                max_allowed_padding = 200
                min_allowed_padding = 10
                mode = "中等窗口"
            else:
                max_allowed_padding = 500  # 增大最大值以实现真正的1/3
                min_allowed_padding = 20
                mode = "大窗口"
            
            # 使用计算出的padding，但限制在合理范围内
            padding = max(min_allowed_padding, min(max_allowed_padding, required_padding_per_side))
            
            print(f"🎛️  {mode}模式:")
            print(f"   - 需要padding: {required_padding_per_side}px")
            print(f"   - 实际padding: {padding}px")
            print(f"   - 允许范围: {min_allowed_padding}-{max_allowed_padding}px")
        
        # 最终安全验证
        final_text_width = max_chars * (char_width * font_size // 12) + text_padding
        calculated_width = final_text_width + padding * 2
        
        if calculated_width > target_tab_width:
            # 重新调整以确保不超过目标宽度
            max_safe_padding = (target_tab_width - final_text_width) // 2
            padding = max(1, max_safe_padding)
            calculated_width = final_text_width + padding * 2
            print(f"🔧 安全调整: padding={padding}px, 计算宽度={calculated_width}px")
        
        # 应用样式配置
        style = ttk.Style()
        style.configure(
            'Modern.TNotebook.Tab',
            padding=[padding, 10],
            font=('Microsoft YaHei', font_size, 'bold'),
            background='#e9ecef',
            foreground='#495057',
            anchor='center'
        )
        
        style.map(
            'Modern.TNotebook.Tab',
            background=[('selected', '#ffffff'), ('active', '#dee2e6')],
            foreground=[('selected', '#0d6efd'), ('active', '#495057')],
            padding=[('selected', [padding, 10]), ('!selected', [padding, 10])]
        )
        
        # 强制刷新显示
        self.notebook.update_idletasks()
        
        # 计算最终结果和状态
        actual_percentage = (calculated_width / container_width * 100) if container_width > 0 else 0
        target_percentage = 100 / tab_count
        deviation = abs(actual_percentage - target_percentage)
        
        # 状态判断
        if calculated_width > target_tab_width:
            status = "⚠️ 可能有文字遮挡"
        elif deviation > 5:
            status = "⚠️ 偏离1/3目标较多"
        else:
            status = "✅ 配置正常"
        
        print(f"✅ 最终结果:")
        print(f"   - 计算宽度: {calculated_width}px ({actual_percentage:.1f}%)")
        print(f"   - 目标百分比: {target_percentage:.1f}%")
        print(f"   - 偏差: {deviation:.1f}%")
        print(f"   - 状态: {status}")
        
    except Exception as e:
        print(f"❌ 配置选项卡样式时出错: {e}")
        import traceback
        traceback.print_exc()
