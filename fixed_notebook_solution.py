#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彻底解决选项卡宽度和文字显示问题的方案
"""

import ttkbootstrap as ttk
from ttkbootstrap import Style

class FixedNotebookSolution:
    def __init__(self):
        self.window = ttk.Window(themename="cosmo")
        self.window.title("选项卡问题终极解决方案")
        self.window.geometry("800x600")
        
        self.style = Style()
        self.setup_styles()
        self.create_interface()
        
        # 绑定事件
        self.window.bind('<Configure>', self.on_resize)
        
        # 初始配置
        self.window.after(100, self.configure_tabs)
        
    def setup_styles(self):
        """设置基础样式"""
        # 清除可能的冲突样式
        self.style.configure('Fixed.TNotebook', background='#f8f9fa', borderwidth=0)
        self.style.configure('Fixed.TNotebook.Tab', 
                           font=('Microsoft YaHei', 12, 'bold'),
                           background='#e9ecef',
                           foreground='#495057')
        
    def create_interface(self):
        """创建界面"""
        # 主容器
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        ttk.Label(
            main_frame,
            text="选项卡问题终极解决方案",
            font=('Microsoft YaHei', 16, 'bold'),
            foreground='#0d6efd'
        ).pack(pady=(0, 10))
        
        # 说明
        ttk.Label(
            main_frame,
            text="这个方案彻底解决文字遮挡和1/3宽度分布问题",
            font=('Microsoft YaHei', 11),
            foreground='#6c757d'
        ).pack(pady=(0, 10))
        
        # 控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill="x", pady=(0, 10))
        
        # 测试按钮
        test_sizes = [
            ("400px", 400, 300),
            ("600px", 600, 400), 
            ("800px", 800, 600),
            ("1000px", 1000, 700),
            ("1200px", 1200, 800),
            ("1600px", 1600, 1000)
        ]
        
        for text, w, h in test_sizes:
            ttk.Button(
                control_frame,
                text=text,
                command=lambda width=w, height=h: self.test_size(width, height),
                width=8
            ).pack(side="left", padx=2)
        
        # 信息显示
        self.info_label = ttk.Label(
            main_frame,
            text="信息加载中...",
            font=('Consolas', 10),
            foreground='#495057',
            justify='left'
        )
        self.info_label.pack(pady=(0, 10))
        
        # 选项卡容器
        self.notebook_container = ttk.Frame(main_frame)
        self.notebook_container.pack(fill="both", expand=True)
        self.notebook_container.grid_rowconfigure(0, weight=1)
        self.notebook_container.grid_columnconfigure(0, weight=1)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(self.notebook_container, style='Fixed.TNotebook')
        self.notebook.grid(row=0, column=0, sticky="nsew")
        
        # 添加选项卡
        self.tab_names = ["单任务", "多任务", "设置"]
        for name in self.tab_names:
            tab = ttk.Frame(self.notebook)
            self.notebook.add(tab, text=name)
            
            # 选项卡内容
            ttk.Label(
                tab,
                text=f"{name} 选项卡\n\n检查标题是否完整显示\n以及是否实现1/3宽度分布",
                font=('Microsoft YaHei', 12),
                foreground='#495057',
                justify='center'
            ).pack(expand=True)
    
    def test_size(self, width, height):
        """测试指定窗口大小"""
        self.window.geometry(f"{width}x{height}")
        self.window.after(100, self.configure_tabs)
    
    def configure_tabs(self):
        """配置选项卡 - 终极解决方案"""
        try:
            print("\n" + "="*60)
            print("🔧 开始终极选项卡配置...")
            
            # 强制更新所有组件
            self.window.update_idletasks()
            self.notebook_container.update_idletasks()
            self.notebook.update_idletasks()
            
            # 获取精确尺寸
            window_width = self.window.winfo_width()
            container_width = self.notebook_container.winfo_width()
            tab_count = len(self.tab_names)
            
            print(f"📏 窗口宽度: {window_width}px")
            print(f"📐 容器宽度: {container_width}px")
            print(f"📊 选项卡数量: {tab_count}")
            
            # 如果容器宽度无效，使用窗口宽度估算
            if container_width <= 1:
                container_width = window_width - 40
                print(f"⚠️  使用估算容器宽度: {container_width}px")
            
            # 计算真正的1/3宽度
            ideal_tab_width = container_width / tab_count
            print(f"🎯 理想选项卡宽度: {ideal_tab_width:.1f}px ({100/tab_count:.1f}%)")
            
            # 精确计算文本宽度
            # 使用更保守的估算来确保文字不被遮挡
            max_chars = max(len(name) for name in self.tab_names)
            font_size = 12
            char_width = 13  # 保守估算每个中文字符宽度
            text_base_width = max_chars * char_width
            text_padding = 20  # 文本内部边距
            min_text_width = text_base_width + text_padding
            
            print(f"📝 文本宽度计算:")
            print(f"   - 最大字符数: {max_chars}")
            print(f"   - 字符宽度: {char_width}px")
            print(f"   - 基础宽度: {text_base_width}px")
            print(f"   - 最小文本宽度: {min_text_width}px")
            
            # 计算可用于padding的空间
            available_for_padding = ideal_tab_width - min_text_width
            print(f"💾 可用padding空间: {available_for_padding:.1f}px")
            
            # 智能padding计算 - 确保文字不被遮挡
            if available_for_padding <= 0:
                # 空间严重不足
                padding = 1
                font_size = 10  # 减小字体
                print(f"⚠️  空间严重不足，使用最小配置")
            elif available_for_padding < 10:
                # 空间不足
                padding = max(1, int(available_for_padding // 2))
                font_size = 11
                print(f"⚠️  空间不足，使用紧凑配置")
            else:
                # 有足够空间，根据窗口大小分级
                if container_width < 500:
                    padding = max(2, min(8, int(available_for_padding // 2)))
                    font_size = 11
                    mode = "超小窗口"
                elif container_width < 800:
                    padding = max(5, min(20, int(available_for_padding // 2)))
                    font_size = 12
                    mode = "小窗口"
                elif container_width < 1200:
                    padding = max(10, min(50, int(available_for_padding // 2)))
                    font_size = 12
                    mode = "中等窗口"
                else:
                    padding = max(20, min(100, int(available_for_padding // 2)))
                    font_size = 13
                    mode = "大窗口"
                
                print(f"🎛️  {mode}模式")
            
            print(f"🔧 最终配置: padding={padding}px, font_size={font_size}px")
            
            # 应用样式配置
            self.style.configure(
                'Fixed.TNotebook.Tab',
                padding=[padding, 8],
                font=('Microsoft YaHei', font_size, 'bold'),
                background='#e9ecef',
                foreground='#495057',
                anchor='center'
            )
            
            # 配置状态映射
            self.style.map(
                'Fixed.TNotebook.Tab',
                background=[('selected', '#ffffff'), ('active', '#dee2e6')],
                foreground=[('selected', '#0d6efd'), ('active', '#495057')],
                padding=[('selected', [padding, 8]), ('!selected', [padding, 8])]
            )
            
            # 强制刷新显示
            self.notebook.update_idletasks()
            
            # 计算最终结果
            final_text_width = max_chars * (char_width * font_size // 12) + text_padding
            final_tab_width = final_text_width + padding * 2
            actual_percentage = (final_tab_width / container_width * 100) if container_width > 0 else 0
            target_percentage = 100 / tab_count
            deviation = abs(actual_percentage - target_percentage)
            
            print(f"✅ 最终结果:")
            print(f"   - 最终选项卡宽度: {final_tab_width}px")
            print(f"   - 实际百分比: {actual_percentage:.1f}%")
            print(f"   - 目标百分比: {target_percentage:.1f}%")
            print(f"   - 偏差: {deviation:.1f}%")
            
            # 状态判断
            if final_tab_width > ideal_tab_width:
                status = "⚠️ 可能有文字遮挡"
            elif deviation > 5:
                status = "⚠️ 偏离1/3目标较多"
            else:
                status = "✅ 配置正常"
            
            print(f"📊 状态: {status}")
            
            # 更新信息显示
            info_lines = [
                f"窗口: {window_width}px | 容器: {container_width}px",
                f"目标宽度: {ideal_tab_width:.1f}px ({target_percentage:.1f}%)",
                f"实际宽度: {final_tab_width}px ({actual_percentage:.1f}%)",
                f"文本宽度: {final_text_width}px | Padding: {padding}px",
                f"字体大小: {font_size}px | 偏差: {deviation:.1f}%",
                status
            ]
            
            self.info_label.config(text="\n".join(info_lines))
            
        except Exception as e:
            print(f"❌ 配置出错: {e}")
            import traceback
            traceback.print_exc()
            self.info_label.config(text=f"配置错误: {e}")
    
    def on_resize(self, event):
        """窗口大小变化事件"""
        if event.widget == self.window:
            print(f"🔄 窗口大小变化: {event.width}x{event.height}")
            self.window.after(50, self.configure_tabs)
    
    def run(self):
        """运行程序"""
        print("🚀 选项卡问题终极解决方案启动！")
        print("请测试不同窗口大小，验证文字显示和1/3宽度分布")
        self.window.mainloop()

if __name__ == "__main__":
    app = FixedNotebookSolution()
    app.run()
