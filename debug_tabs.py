#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试选项卡宽度和文字显示问题
"""

import ttkbootstrap as ttk
from ttkbootstrap import Style

class TabDebugger:
    def __init__(self):
        self.window = ttk.Window(themename="cosmo")
        self.window.title("选项卡调试器")
        self.window.geometry("800x600")
        
        self.style = Style()
        self.create_interface()
        
        # 绑定事件
        self.window.bind('<Configure>', self.on_resize)
        
        # 初始配置
        self.window.after(100, self.update_tabs)
        
    def create_interface(self):
        """创建界面"""
        # 主容器
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        ttk.Label(
            main_frame,
            text="选项卡宽度和文字显示调试器",
            font=('Microsoft YaHei', 14, 'bold'),
            foreground='#0d6efd'
        ).pack(pady=(0, 10))
        
        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="测试控制", padding=10)
        control_frame.pack(fill="x", pady=(0, 10))
        
        # 窗口大小按钮
        size_frame = ttk.Frame(control_frame)
        size_frame.pack(fill="x", pady=(0, 5))
        
        ttk.Label(size_frame, text="窗口大小:").pack(side="left")
        
        sizes = [
            ("400x300", 400, 300),
            ("600x400", 600, 400),
            ("800x600", 800, 600),
            ("1000x700", 1000, 700),
            ("1200x800", 1200, 800),
            ("1600x1000", 1600, 1000)
        ]
        
        for text, w, h in sizes:
            ttk.Button(
                size_frame,
                text=text,
                command=lambda width=w, height=h: self.set_size(width, height),
                width=10
            ).pack(side="left", padx=2)
        
        # 手动刷新按钮
        ttk.Button(
            control_frame,
            text="手动刷新",
            command=self.update_tabs,
            bootstyle="primary"
        ).pack(pady=5)
        
        # 信息显示
        self.info_frame = ttk.LabelFrame(main_frame, text="实时信息", padding=10)
        self.info_frame.pack(fill="x", pady=(0, 10))
        
        self.info_text = ttk.Label(
            self.info_frame,
            text="信息加载中...",
            font=('Consolas', 9),
            foreground='#495057',
            justify='left'
        )
        self.info_text.pack(anchor="w")
        
        # 选项卡容器
        self.notebook_container = ttk.Frame(main_frame)
        self.notebook_container.pack(fill="both", expand=True)
        self.notebook_container.grid_rowconfigure(0, weight=1)
        self.notebook_container.grid_columnconfigure(0, weight=1)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(self.notebook_container)
        self.notebook.grid(row=0, column=0, sticky="nsew")
        
        # 添加选项卡
        for name in ["单任务", "多任务", "设置"]:
            tab = ttk.Frame(self.notebook)
            self.notebook.add(tab, text=name)
            
            # 选项卡内容
            content_frame = ttk.Frame(tab)
            content_frame.pack(fill="both", expand=True, padx=20, pady=20)
            
            ttk.Label(
                content_frame,
                text=f"{name} 选项卡",
                font=('Microsoft YaHei', 16, 'bold'),
                foreground='#0d6efd'
            ).pack(pady=(0, 10))
            
            ttk.Label(
                content_frame,
                text="检查选项卡标题是否完整显示\n以及是否实现1/3宽度分布",
                font=('Microsoft YaHei', 11),
                foreground='#495057',
                justify='center'
            ).pack()
    
    def set_size(self, width, height):
        """设置窗口大小"""
        self.window.geometry(f"{width}x{height}")
        print(f"🔧 设置窗口大小: {width}x{height}")
        self.window.after(100, self.update_tabs)
    
    def update_tabs(self):
        """更新选项卡样式"""
        try:
            print("\n" + "="*50)
            print("🔧 开始更新选项卡样式...")
            
            # 强制更新所有组件
            self.window.update_idletasks()
            self.notebook_container.update_idletasks()
            self.notebook.update_idletasks()
            
            # 获取尺寸信息
            window_width = self.window.winfo_width()
            window_height = self.window.winfo_height()
            container_width = self.notebook_container.winfo_width()
            container_height = self.notebook_container.winfo_height()
            tab_count = self.notebook.index("end")
            
            print(f"📏 窗口尺寸: {window_width}x{window_height}")
            print(f"📐 容器尺寸: {container_width}x{container_height}")
            print(f"📊 选项卡数量: {tab_count}")
            
            if container_width <= 1:
                container_width = window_width - 40
                print(f"⚠️  容器宽度无效，使用估算值: {container_width}")
            
            # 计算目标宽度（真正的1/3）
            target_width_per_tab = container_width / tab_count
            target_width_int = int(target_width_per_tab)
            
            print(f"🎯 目标宽度: {target_width_per_tab:.1f}px ({target_width_per_tab/container_width*100:.1f}%)")
            
            # 文本宽度计算
            tab_texts = ["单任务", "多任务", "设置"]
            max_chars = max(len(text) for text in tab_texts)
            char_width = 14  # 每个中文字符的宽度
            text_margin = 16  # 文本边距
            text_width = max_chars * char_width + text_margin
            
            print(f"📝 文本宽度计算: {max_chars}字符 × {char_width}px + {text_margin}px = {text_width}px")
            
            # 计算可用于padding的空间
            available_space = target_width_int - text_width
            print(f"💾 可用空间: {target_width_int} - {text_width} = {available_space}px")
            
            # 智能padding计算
            if available_space <= 0:
                padding = 1  # 最小padding
                print(f"⚠️  空间不足，使用最小padding: {padding}px")
            else:
                # 根据容器宽度分级
                if container_width < 500:
                    padding = max(1, min(5, available_space // 2))
                    mode = "超小窗口"
                elif container_width < 800:
                    padding = max(3, min(15, available_space // 2))
                    mode = "小窗口"
                elif container_width < 1200:
                    padding = max(8, min(40, available_space // 2))
                    mode = "中等窗口"
                else:
                    padding = max(15, min(80, available_space // 2))
                    mode = "大窗口"
                
                print(f"🎛️  {mode}模式，计算padding: {padding}px")
            
            # 最终验证
            actual_width = text_width + padding * 2
            if actual_width > target_width_int:
                # 重新调整padding
                max_padding = (target_width_int - text_width) // 2
                padding = max(1, max_padding)
                actual_width = text_width + padding * 2
                print(f"🔧 调整padding以适应目标: {padding}px")
            
            # 应用样式
            self.style.configure(
                'TNotebook.Tab',
                padding=[padding, 10],
                font=('Microsoft YaHei', 12, 'bold'),
                background='#e9ecef',
                foreground='#495057',
                anchor='center'
            )
            
            self.style.map(
                'TNotebook.Tab',
                background=[('selected', '#ffffff'), ('active', '#dee2e6')],
                foreground=[('selected', '#0d6efd'), ('active', '#495057')],
                padding=[('selected', [padding, 10]), ('!selected', [padding, 10])]
            )
            
            # 强制刷新
            self.notebook.update_idletasks()
            
            # 计算结果
            width_percentage = (actual_width / container_width * 100) if container_width > 0 else 0
            target_percentage = 100 / tab_count
            
            print(f"✅ 最终结果:")
            print(f"   - 实际宽度: {actual_width}px ({width_percentage:.1f}%)")
            print(f"   - 目标百分比: {target_percentage:.1f}%")
            print(f"   - 偏差: {abs(width_percentage - target_percentage):.1f}%")
            
            # 更新信息显示
            info_lines = [
                f"窗口: {window_width}x{window_height}px",
                f"容器: {container_width}x{container_height}px",
                f"目标宽度: {target_width_int}px ({target_percentage:.1f}%)",
                f"实际宽度: {actual_width}px ({width_percentage:.1f}%)",
                f"文本宽度: {text_width}px",
                f"Padding: {padding}px",
                f"偏差: {abs(width_percentage - target_percentage):.1f}%"
            ]
            
            if abs(width_percentage - target_percentage) > 3:
                info_lines.append("⚠️ 偏差较大")
            elif actual_width > target_width_int:
                info_lines.append("⚠️ 可能有文字遮挡")
            else:
                info_lines.append("✅ 显示正常")
            
            self.info_text.config(text="\n".join(info_lines))
            
        except Exception as e:
            print(f"❌ 更新出错: {e}")
            import traceback
            traceback.print_exc()
            self.info_text.config(text=f"错误: {e}")
    
    def on_resize(self, event):
        """窗口大小变化事件"""
        if event.widget == self.window:
            print(f"🔄 窗口大小变化: {event.width}x{event.height}")
            self.window.after(50, self.update_tabs)
    
    def run(self):
        """运行程序"""
        print("选项卡调试器启动！")
        print("请使用按钮或手动调整窗口大小来测试")
        self.window.mainloop()

if __name__ == "__main__":
    app = TabDebugger()
    app.run()
