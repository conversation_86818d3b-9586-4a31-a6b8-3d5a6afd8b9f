import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from tkinter import messagebox, filedialog
import pandas as pd
from datetime import datetime
from calculator_core import CeramicCalculatorCore
from ui_styles import apply_modern_theme


class CeramicCalculatorUI:

    def __init__(self):
        # 创建计算核心实例
        self.calculator_core = CeramicCalculatorCore()

        # 设置主题和颜色模式
        self.window = ttk.Window(themename="cosmo")
        self.window.title("陶瓷配方计算器")
        self.window.geometry("1600x1000")
        self.window.state('zoomed')

        # 应用现代化主题
        self.style, self.colors, self.fonts = apply_modern_theme(self.window)

        # 存储数据
        self.single_task_result = None
        self.multi_task_results = None
        self.batch_data = []

        # 加载原料数据
        self.calculator_core.load_materials_from_excel()

        self.create_widgets()

    def create_widgets(self):
        """创建主界面组件"""
        # 创建主容器 - 使用百分比布局
        self.main_container = ttk.Frame(self.window)
        self.main_container.pack(fill="both", expand=True)

        # 配置主容器的网格权重
        self.main_container.grid_rowconfigure(1, weight=1)
        self.main_container.grid_columnconfigure(0, weight=1)

        # 创建顶部标题栏 - 固定高度
        self.create_modern_header()

        # 创建主要内容区域 - 自适应
        self.create_main_content()

    def create_modern_header(self):
        """创建现代化的标题栏"""
        header_frame = ttk.Frame(self.main_container, style='Header.TFrame')
        header_frame.grid(row=0, column=0, sticky="ew", padx=0, pady=0)
        header_frame.grid_columnconfigure(0, weight=1)

        # 标题区域
        title_frame = ttk.Frame(header_frame)
        title_frame.grid(row=0, column=0, sticky="", padx=30, pady=20)

        # 主标题
        main_title = ttk.Label(
            title_frame,
            text="陶瓷配方计算器",
            font=self.fonts['title'],
            foreground=self.colors['primary']
        )
        main_title.pack()


    def create_main_content(self):
        """创建主要内容区域"""
        # 内容容器
        content_container = ttk.Frame(self.main_container)
        content_container.grid(row=1, column=0, sticky="nsew", padx=20, pady=(0, 20))
        content_container.grid_rowconfigure(0, weight=1)
        content_container.grid_columnconfigure(0, weight=1)

        # 创建选项卡控件 - 使用现代化样式，确保选项卡均匀分布
        self.notebook = ttk.Notebook(content_container, style='Modern.TNotebook', width=800)
        self.notebook.grid(row=0, column=0, sticky="nsew")

        # 确保选项卡能够均匀分布
        self.notebook.grid_columnconfigure(0, weight=1)

        # 单任务选项卡
        self.single_task_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.single_task_tab, text="单任务")

        # 多任务选项卡
        self.multi_task_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.multi_task_tab, text="多任务")

        # 设置选项卡
        self.settings_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_tab, text="设置")

        # 配置选项卡样式以实现居中均匀分布
        self.configure_notebook_tabs()

        # 创建各个选项卡内容
        self.create_single_task_tab()
        self.create_multi_task_tab()
        self.create_settings_tab()

    def configure_notebook_tabs(self):
        """配置选项卡样式以实现居中均匀分布"""
        try:
            # 确保选项卡样式已正确应用
            # 由于我们在ui_styles.py中已经配置了大的padding，
            # 这里只需要确保样式被正确应用
            self.notebook.update_idletasks()

            # 可选：动态调整选项卡宽度
            tab_count = self.notebook.index("end")
            print(f"已配置 {tab_count} 个选项卡，使用均匀分布样式")

        except Exception as e:
            print(f"配置选项卡样式时出错: {e}")

    def create_single_task_tab(self):
        """创建单任务计算选项卡"""
        # 配置网格权重
        self.single_task_tab.grid_rowconfigure(0, weight=1)
        self.single_task_tab.grid_columnconfigure(0, weight=1)

        # 主容器
        main_frame = ttk.Frame(self.single_task_tab)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=30, pady=30)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # 输入区域 - 固定高度
        self.create_single_input_section(main_frame)

        # 结果区域 - 自适应高度
        self.create_single_result_section(main_frame)

    def create_single_input_section(self, parent):
        """创建单任务输入区域"""
        input_frame = ttk.LabelFrame(
            parent,
            text="化学式输入",
            style='Modern.TLabelframe',
            padding=25
        )
        input_frame.grid(row=0, column=0, sticky="ew", pady=(0, 20))
        input_frame.grid_columnconfigure(1, weight=1)

        # 化学式输入
        ttk.Label(
            input_frame,
            text="化学式:",
            font=self.fonts['body']
        ).grid(row=0, column=0, sticky="w", pady=(0, 15))

        self.single_formula_entry = ttk.Entry(
            input_frame,
            font=self.fonts['body'],
            width=60
        )
        self.single_formula_entry.grid(row=0, column=1, sticky="ew", padx=(15, 0), pady=(0, 15))
        self.single_formula_entry.insert(0, "(Bi0.5Ba0.1Sr0.1Ca0.2Na0.1)(Fe0.5Ti0.3Zr0.1Nb0.1)O3")

        # 总质量输入
        ttk.Label(
            input_frame,
            text="总质量(g):",
            font=self.fonts['body']
        ).grid(row=1, column=0, sticky="w", pady=(0, 20))

        self.single_mass_entry = ttk.Entry(
            input_frame,
            font=self.fonts['body'],
            width=20
        )
        self.single_mass_entry.grid(row=1, column=1, sticky="w", padx=(15, 0), pady=(0, 20))
        self.single_mass_entry.insert(0, "50")

        # 计算按钮
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=2, column=0, columnspan=2, sticky="ew")

        self.single_calculate_btn = ttk.Button(
            button_frame,
            text="开始计算",
            command=self.calculate_single_task,
            bootstyle="primary",
            style='Large.TButton',
            width=15
        )
        self.single_calculate_btn.pack(side="right", padx=(10, 0))

        # 示例按钮
        ttk.Button(
            button_frame,
            text="加载示例",
            command=self.load_single_example,
            bootstyle="info-outline",
            width=12
        ).pack(side="right")

    def create_single_result_section(self, parent):
        """创建单任务结果区域"""
        result_frame = ttk.LabelFrame(
            parent,
            text="计算结果",
            style='Modern.TLabelframe',
            padding=25
        )
        result_frame.grid(row=1, column=0, sticky="nsew")
        result_frame.grid_rowconfigure(0, weight=1)
        result_frame.grid_columnconfigure(0, weight=1)

        # 结果容器
        self.single_result_container = ttk.Frame(result_frame)
        self.single_result_container.grid(row=0, column=0, sticky="nsew")
        self.single_result_container.grid_rowconfigure(0, weight=1)
        self.single_result_container.grid_columnconfigure(0, weight=1)

        # 初始提示
        self.show_single_initial_message()

    def show_single_initial_message(self):
        """显示单任务初始提示"""
        # 清空容器
        for widget in self.single_result_container.winfo_children():
            widget.destroy()

        # 提示信息
        message_frame = ttk.Frame(self.single_result_container)
        message_frame.grid(row=0, column=0, sticky="nsew")

        ttk.Label(
            message_frame,
            text="💡 请输入化学式和总质量，然后点击\"开始计算\"",
            font=self.fonts['heading'],
            foreground=self.colors['info'],
            anchor="center"
        ).pack(expand=True)

    def load_single_example(self):
        """加载单任务示例"""
        examples = [
            ("(Bi0.5Ba0.1Sr0.1Ca0.2Na0.1)(Fe0.5Ti0.3Zr0.1Nb0.1)O3", "50"),
            ("0.9(0.55Na0.5Bi0.5TiO3-0.45Ba0.85Ca0.15Zr0.1Ti0.9O3)-0.1Bi(Mg2/3Ta1/3)O3", "100"),
            ("BaTiO3", "25")
        ]

        import random
        formula, mass = random.choice(examples)

        self.single_formula_entry.delete(0, 'end')
        self.single_formula_entry.insert(0, formula)
        self.single_mass_entry.delete(0, 'end')
        self.single_mass_entry.insert(0, mass)

    def calculate_single_task(self):
        """执行单任务计算"""
        try:
            # 获取输入
            formula = self.single_formula_entry.get().strip()
            if not formula:
                messagebox.showerror("输入错误", "请输入化学式")
                return

            try:
                mass = float(self.single_mass_entry.get().strip())
                if mass <= 0:
                    raise ValueError
            except ValueError:
                messagebox.showerror("输入错误", "总质量必须是正数")
                return

            # 计算
            result = self.calculator_core.calculate_multiple_formulas([(formula, mass)])
            self.single_task_result = result

            # 显示结果
            self.show_single_result(result['results'][0], result['all_materials'])

        except Exception as e:
            messagebox.showerror("计算错误", str(e))

    def show_single_result(self, result, all_materials):
        """显示单任务计算结果"""
        # 清空容器
        for widget in self.single_result_container.winfo_children():
            widget.destroy()

        # 结果表格容器
        table_frame = ttk.Frame(self.single_result_container)
        table_frame.grid(row=0, column=0, sticky="nsew", pady=(0, 15))
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # 创建结果表格
        columns = ["原料名称", "所需质量(g)", "百分比(%)"]
        self.single_result_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=12
        )

        # 设置列
        for col in columns:
            self.single_result_tree.heading(col, text=col)
            if col == "原料名称":
                self.single_result_tree.column(col, width=300, anchor="w")
            else:
                self.single_result_tree.column(col, width=150, anchor="center")

        # 添加滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.single_result_tree.yview)
        self.single_result_tree.configure(yscrollcommand=scrollbar.set)

        # 填充数据
        total_mass = sum(result['materials'].values())
        for material in sorted(result['materials'].keys()):
            mass = result['materials'][material]
            percentage = (mass / total_mass) * 100 if total_mass > 0 else 0
            self.single_result_tree.insert("", "end", values=[
                material,
                f"{mass:.4f}",
                f"{percentage:.2f}%"
            ])

        # 布局
        self.single_result_tree.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")

        # 导出按钮
        export_frame = ttk.Frame(self.single_result_container)
        export_frame.grid(row=1, column=0, sticky="ew")

        ttk.Button(
            export_frame,
            text="导出Excel",
            command=self.export_single_result,
            bootstyle="success",
            width=15
        ).pack(side="right")

    def export_single_result(self):
        """导出单任务结果"""
        if not self.single_task_result:
            messagebox.showerror("导出错误", "请先进行计算")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel 文件", "*.xlsx")],
                initialfile=f"单任务计算结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )

            if not file_path:
                return

            result = self.single_task_result['results'][0]

            # 创建数据
            data = []
            total_mass = sum(result['materials'].values())

            for material in sorted(result['materials'].keys()):
                mass = result['materials'][material]
                percentage = (mass / total_mass) * 100 if total_mass > 0 else 0
                data.append({
                    '原料名称': material,
                    '所需质量(g)': round(mass, 4),
                    '百分比(%)': round(percentage, 2)
                })

            df = pd.DataFrame(data)

            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='计算结果', index=False)

                # 添加计算信息
                info_data = [{
                    '化学式': result['formula'],
                    '目标质量(g)': result['target_mass'],
                    '计算时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }]
                info_df = pd.DataFrame(info_data)
                info_df.to_excel(writer, sheet_name='计算信息', index=False)

            messagebox.showinfo("导出成功", f"结果已保存到:\n{file_path}")

        except Exception as e:
            messagebox.showerror("导出错误", f"导出时出错: {str(e)}")

    def create_multi_task_tab(self):
        """创建多任务批量选项卡"""
        # 配置网格权重
        self.multi_task_tab.grid_rowconfigure(0, weight=1)
        self.multi_task_tab.grid_columnconfigure(0, weight=1)

        # 主容器
        main_frame = ttk.Frame(self.multi_task_tab)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=30, pady=30)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # 输入区域
        self.create_multi_input_section(main_frame)

        # 结果区域
        self.create_multi_result_section(main_frame)

    def create_multi_input_section(self, parent):
        """创建多任务输入区域"""
        input_frame = ttk.LabelFrame(
            parent,
            text="批量导入与计算",
            style='Modern.TLabelframe',
            padding=25
        )
        input_frame.grid(row=0, column=0, sticky="ew", pady=(0, 20))
        input_frame.grid_columnconfigure(1, weight=1)

        # 导入方式选择
        method_frame = ttk.Frame(input_frame)
        method_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 20))

        ttk.Label(
            method_frame,
            text="导入方式:",
            font=self.fonts['body']
        ).pack(side="left")

        # 手动输入按钮
        ttk.Button(
            method_frame,
            text="手动添加",
            command=self.add_multi_row,
            bootstyle="info-outline",
            width=12
        ).pack(side="left", padx=(15, 10))

        # Excel导入按钮
        ttk.Button(
            method_frame,
            text="Excel导入",
            command=self.import_from_excel,
            bootstyle="success-outline",
            width=12
        ).pack(side="left", padx=(0, 10))

        # 清空按钮
        ttk.Button(
            method_frame,
            text="清空所有",
            command=self.clear_multi_data,
            bootstyle="danger-outline",
            width=12
        ).pack(side="left")

        # 数据表格区域
        table_frame = ttk.Frame(input_frame)
        table_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 20))
        table_frame.grid_columnconfigure(0, weight=1)

        # 表格
        columns = ["序号", "化学式", "总质量(g)", "操作"]
        self.multi_input_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=8
        )

        # 设置列
        for col in columns:
            self.multi_input_tree.heading(col, text=col)
            if col == "序号":
                self.multi_input_tree.column(col, width=60, anchor="center")
            elif col == "化学式":
                self.multi_input_tree.column(col, width=400, anchor="w")
            elif col == "总质量(g)":
                self.multi_input_tree.column(col, width=120, anchor="center")
            else:
                self.multi_input_tree.column(col, width=80, anchor="center")

        # 滚动条
        multi_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.multi_input_tree.yview)
        self.multi_input_tree.configure(yscrollcommand=multi_scrollbar.set)

        self.multi_input_tree.grid(row=0, column=0, sticky="ew")
        multi_scrollbar.grid(row=0, column=1, sticky="ns")

        # 计算按钮
        calc_frame = ttk.Frame(input_frame)
        calc_frame.grid(row=2, column=0, columnspan=2, sticky="ew")

        self.multi_calculate_btn = ttk.Button(
            calc_frame,
            text="批量计算",
            command=self.calculate_multi_task,
            bootstyle="primary",
            style='Large.TButton',
            width=15
        )
        self.multi_calculate_btn.pack(side="right")

    def create_multi_result_section(self, parent):
        """创建多任务结果区域"""
        result_frame = ttk.LabelFrame(
            parent,
            text="批量计算结果",
            style='Modern.TLabelframe',
            padding=25
        )
        result_frame.grid(row=1, column=0, sticky="nsew")
        result_frame.grid_rowconfigure(0, weight=1)
        result_frame.grid_columnconfigure(0, weight=1)

        # 结果容器
        self.multi_result_container = ttk.Frame(result_frame)
        self.multi_result_container.grid(row=0, column=0, sticky="nsew")
        self.multi_result_container.grid_rowconfigure(0, weight=1)
        self.multi_result_container.grid_columnconfigure(0, weight=1)

        # 初始提示
        self.show_multi_initial_message()

    def show_multi_initial_message(self):
        """显示多任务初始提示"""
        # 清空容器
        for widget in self.multi_result_container.winfo_children():
            widget.destroy()

        message_frame = ttk.Frame(self.multi_result_container)
        message_frame.grid(row=0, column=0, sticky="nsew")

        ttk.Label(
            message_frame,
            text="📊 请添加化学式数据，然后点击\"批量计算\"",
            font=self.fonts['heading'],
            foreground=self.colors['info'],
            anchor="center"
        ).pack(expand=True)

    def add_multi_row(self):
        """添加多任务行"""
        # 简单的输入对话框
        dialog = ttk.Toplevel(self.window)
        dialog.title("添加化学式")
        dialog.geometry("600x200")
        dialog.transient(self.window)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (
            self.window.winfo_rootx() + 50,
            self.window.winfo_rooty() + 50
        ))

        main_frame = ttk.Frame(dialog, padding=20)
        main_frame.pack(fill="both", expand=True)

        # 化学式输入
        ttk.Label(main_frame, text="化学式:", font=self.fonts['body']).grid(row=0, column=0, sticky="w", pady=(0, 10))
        formula_entry = ttk.Entry(main_frame, font=self.fonts['body'], width=50)
        formula_entry.grid(row=0, column=1, sticky="ew", padx=(10, 0), pady=(0, 10))

        # 质量输入
        ttk.Label(main_frame, text="总质量(g):", font=self.fonts['body']).grid(row=1, column=0, sticky="w", pady=(0, 20))
        mass_entry = ttk.Entry(main_frame, font=self.fonts['body'], width=20)
        mass_entry.grid(row=1, column=1, sticky="w", padx=(10, 0), pady=(0, 20))

        main_frame.grid_columnconfigure(1, weight=1)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, sticky="ew")

        def add_data():
            formula = formula_entry.get().strip()
            try:
                mass = float(mass_entry.get().strip())
                if formula and mass > 0:
                    self.batch_data.append((formula, mass))
                    self.refresh_multi_input_table()
                    dialog.destroy()
                else:
                    messagebox.showerror("输入错误", "请输入有效的化学式和正数质量")
            except ValueError:
                messagebox.showerror("输入错误", "质量必须是数字")

        ttk.Button(button_frame, text="添加", command=add_data, bootstyle="primary").pack(side="right", padx=(10, 0))
        ttk.Button(button_frame, text="取消", command=dialog.destroy, bootstyle="secondary").pack(side="right")

    def import_from_excel(self):
        """从Excel导入批量数据"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx"), ("Excel文件", "*.xls")],
            initialdir="."
        )

        if not file_path:
            return

        try:
            df = pd.read_excel(file_path)

            # 检查必要的列
            required_columns = ['化学式', '总质量']
            if not all(col in df.columns for col in required_columns):
                messagebox.showerror(
                    "格式错误",
                    f"Excel文件必须包含以下列: {', '.join(required_columns)}"
                )
                return

            # 导入数据
            imported_count = 0
            for _, row in df.iterrows():
                formula = str(row['化学式']).strip()
                try:
                    mass = float(row['总质量'])
                    if formula and mass > 0:
                        self.batch_data.append((formula, mass))
                        imported_count += 1
                except (ValueError, TypeError):
                    continue

            self.refresh_multi_input_table()
            messagebox.showinfo("导入成功", f"成功导入 {imported_count} 条数据")

        except Exception as e:
            messagebox.showerror("导入失败", f"导入Excel文件时出错: {str(e)}")

    def clear_multi_data(self):
        """清空多任务数据"""
        if self.batch_data:
            if messagebox.askyesno("确认清空", "确定要清空所有数据吗？"):
                self.batch_data.clear()
                self.refresh_multi_input_table()
                self.show_multi_initial_message()

    def refresh_multi_input_table(self):
        """刷新多任务输入表格"""
        # 清空表格
        for item in self.multi_input_tree.get_children():
            self.multi_input_tree.delete(item)

        # 添加数据
        for i, (formula, mass) in enumerate(self.batch_data):
            self.multi_input_tree.insert("", "end", values=[
                i + 1,
                formula,
                f"{mass:.2f}",
                "删除"
            ])

    def calculate_multi_task(self):
        """执行多任务批量计算"""
        if not self.batch_data:
            messagebox.showerror("数据错误", "请先添加化学式数据")
            return

        try:
            # 执行批量计算
            results = self.calculator_core.calculate_multiple_formulas(self.batch_data)
            self.multi_task_results = results

            # 显示结果
            self.show_multi_results(results)

        except Exception as e:
            messagebox.showerror("计算错误", str(e))

    def show_multi_results(self, results):
        """显示多任务计算结果"""
        # 清空容器
        for widget in self.multi_result_container.winfo_children():
            widget.destroy()

        # 结果表格容器
        table_frame = ttk.Frame(self.multi_result_container)
        table_frame.grid(row=0, column=0, sticky="nsew", pady=(0, 15))
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # 创建结果表格
        formula_results = results['results']
        all_materials = results['all_materials']

        columns = ["原料/化学式"] + [f"配方{i+1}" for i in range(len(formula_results))] + ["总计(g)"]

        self.multi_result_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=15
        )

        # 设置列
        for col in columns:
            self.multi_result_tree.heading(col, text=col)
            if col == "原料/化学式":
                self.multi_result_tree.column(col, width=200, anchor="w")
            else:
                self.multi_result_tree.column(col, width=120, anchor="center")

        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.multi_result_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient="horizontal", command=self.multi_result_tree.xview)
        self.multi_result_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 添加目标质量行
        mass_row = ["目标质量(g)"]
        total_mass = 0
        for result in formula_results:
            mass = result['target_mass']
            total_mass += mass
            mass_row.append(f"{mass:.2f}")
        mass_row.append(f"{total_mass:.2f}")

        self.multi_result_tree.insert("", "end", values=mass_row, tags=("header",))

        # 计算每种原料的总量
        material_totals = {}
        for material in all_materials:
            material_totals[material] = 0
            for result in formula_results:
                if material in result['materials']:
                    material_totals[material] += result['materials'][material]

        # 添加原料行
        for material in sorted(all_materials):
            row = [material]
            for result in formula_results:
                if material in result['materials']:
                    mass = result['materials'][material]
                    row.append(f"{mass:.4f}")
                else:
                    row.append("-")

            # 添加总量
            total = material_totals[material]
            row.append(f"{total:.4f}")

            self.multi_result_tree.insert("", "end", values=row)

        # 设置样式
        self.multi_result_tree.tag_configure("header", background="#e6f3ff", font=self.fonts['body'])

        # 布局
        self.multi_result_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")

        # 导出按钮
        export_frame = ttk.Frame(self.multi_result_container)
        export_frame.grid(row=1, column=0, sticky="ew")

        ttk.Button(
            export_frame,
            text="下载结果",
            command=self.export_multi_results,
            bootstyle="success",
            width=15
        ).pack(side="right")

    def export_multi_results(self):
        """导出多任务结果"""
        if not self.multi_task_results:
            messagebox.showerror("导出错误", "请先进行批量计算")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel 文件", "*.xlsx")],
                initialfile=f"批量计算结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )

            if not file_path:
                return

            results = self.multi_task_results['results']
            materials = self.multi_task_results['all_materials']

            # 创建主数据表
            columns = ['原料/化学式'] + [f'配方{i+1}' for i in range(len(results))] + ['总计(g)']

            data = []

            # 目标质量行
            mass_row = ['目标质量(g)']
            total_mass = sum(result['target_mass'] for result in results)
            for result in results:
                mass_row.append(result['target_mass'])
            mass_row.append(total_mass)
            data.append(mass_row)

            # 计算原料总量
            material_totals = {}
            for material in materials:
                material_totals[material] = sum(
                    result['materials'].get(material, 0) for result in results
                )

            # 原料行
            for material in sorted(materials):
                row = [material]
                for result in results:
                    if material in result['materials']:
                        row.append(round(result['materials'][material], 4))
                    else:
                        row.append(0)
                row.append(round(material_totals[material], 4))
                data.append(row)

            # 创建DataFrame并保存
            df = pd.DataFrame(data, columns=columns)

            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # 主计算结果
                df.to_excel(writer, sheet_name='批量计算结果', index=False)

                # 化学式信息
                formula_data = []
                for i, result in enumerate(results):
                    formula_data.append({
                        '配方编号': f'配方{i+1}',
                        '化学式': result['formula'],
                        '目标质量(g)': result['target_mass']
                    })

                formula_df = pd.DataFrame(formula_data)
                formula_df.to_excel(writer, sheet_name='化学式信息', index=False)

            messagebox.showinfo("导出成功", f"批量计算结果已保存到:\n{file_path}")

        except Exception as e:
            messagebox.showerror("导出错误", f"导出时出错: {str(e)}")

    def create_settings_tab(self):
        """创建设置选项卡"""
        # 配置网格权重
        self.settings_tab.grid_rowconfigure(0, weight=1)
        self.settings_tab.grid_columnconfigure(0, weight=1)

        # 主容器
        main_frame = ttk.Frame(self.settings_tab)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=30, pady=30)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # 操作区域
        self.create_settings_controls(main_frame)

        # 原料表格区域
        self.create_materials_display(main_frame)

    def create_settings_controls(self, parent):
        """创建设置控制区域"""
        control_frame = ttk.LabelFrame(
            parent,
            text="原料数据管理",
            style='Modern.TLabelframe',
            padding=25
        )
        control_frame.grid(row=0, column=0, sticky="ew", pady=(0, 20))

        # 说明文本
        info_frame = ttk.Frame(control_frame)
        info_frame.pack(fill="x", pady=(0, 20))

        ttk.Label(
            info_frame,
            text="原料信息设置",
            font=self.fonts['heading'],
            foreground=self.colors['primary']
        ).pack(anchor="w")

        ttk.Label(
            info_frame,
            text="管理计算所需的原料数据库，支持从Excel文件导入完整的原料信息",
            font=self.fonts['body'],
            foreground=self.colors['secondary']
        ).pack(anchor="w", pady=(5, 0))

        # 操作按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill="x")

        # 左侧按钮组
        left_buttons = ttk.Frame(button_frame)
        left_buttons.pack(side="left")

        ttk.Button(
            left_buttons,
            text="📁 从文件加载",
            command=self.load_materials_from_file,
            bootstyle="info",
            width=18
        ).pack(side="left", padx=(0, 15))

        ttk.Button(
            left_buttons,
            text="🔄 重新加载默认",
            command=self.reload_materials,
            bootstyle="secondary",
            width=18
        ).pack(side="left", padx=(0, 15))

        ttk.Button(
            left_buttons,
            text="📥 下载模板",
            command=self.download_template,
            bootstyle="success",
            width=18
        ).pack(side="left")

    def create_materials_display(self, parent):
        """创建原料显示区域"""
        display_frame = ttk.LabelFrame(
            parent,
            text="原料数据表",
            style='Modern.TLabelframe',
            padding=25
        )
        display_frame.grid(row=1, column=0, sticky="nsew")
        display_frame.grid_rowconfigure(0, weight=1)
        display_frame.grid_columnconfigure(0, weight=1)

        # 表格容器
        table_container = ttk.Frame(display_frame)
        table_container.grid(row=0, column=0, sticky="nsew")
        table_container.grid_rowconfigure(0, weight=1)
        table_container.grid_columnconfigure(0, weight=1)

        # 创建Treeview表格显示所有数据
        if self.calculator_core.raw_materials_data:
            columns = [col for col in self.calculator_core.raw_materials_data[0].keys() if col != '_row_index']
        else:
            columns = ['Element No.', 'Element', 'Ratio', 'Formula', 'Molecular Weight', 'CAS', 'Manufacturer', 'Product No.', 'Purity', 'Pricing', 'Excess']

        # 创建Treeview
        self.materials_tree = ttk.Treeview(
            table_container,
            columns=columns,
            show="headings",
            height=15
        )

        # 设置列标题和宽度
        for col in columns:
            self.materials_tree.heading(col, text=col)
            # 根据列名设置合适的宽度
            if col in ['Element No.', 'Element', 'Ratio', 'Purity', 'Excess']:
                width = 100
            elif col in ['Formula', 'CAS', 'Manufacturer']:
                width = 150
            elif col in ['Product No.', 'Molecular Weight', 'Pricing']:
                width = 140
            else:
                width = 120
            self.materials_tree.column(col, width=width, anchor="center")

        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=self.materials_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_container, orient="horizontal", command=self.materials_tree.xview)
        self.materials_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 填充数据
        self.populate_materials_table()

        # 布局
        self.materials_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")

        # 信息标签
        info_frame = ttk.Frame(display_frame)
        info_frame.grid(row=1, column=0, sticky="ew", pady=(15, 0))

        data_count = len(self.calculator_core.raw_materials_data)
        info_text = f"📊 当前显示 {data_count} 条原料数据记录"
        ttk.Label(
            info_frame,
            text=info_text,
            font=self.fonts['small'],
            foreground=self.colors['info']
        ).pack(anchor="w")

    # 原料管理相关方法
    def load_materials_from_file(self):
        """从指定文件加载原料数据"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx"), ("Excel文件", "*.xls"), ("所有文件", "*.*")],
            initialdir="."
        )

        if file_path:
            try:
                self.calculator_core.load_materials_from_excel(file_path)
                self.refresh_materials_table()
                messagebox.showinfo("✅ 加载成功", f"已从文件加载原料数据:\n{file_path}")
            except Exception as e:
                messagebox.showerror("❌ 加载失败", f"加载文件时出错:\n{str(e)}")

    def reload_materials(self):
        """重新加载默认原料数据"""
        success = self.calculator_core.load_materials_from_excel()
        self.refresh_materials_table()
        if success:
            messagebox.showinfo("✅ 重新加载", "已重新加载默认原料数据")
        else:
            messagebox.showerror(
                "❌ 文件缺失",
                "未找到 initial.xlsx 文件！\n\n请执行以下操作之一：\n• 将Excel数据文件重命名为 'initial.xlsx' 放在程序目录\n• 点击'从文件加载'按钮选择Excel文件\n• 点击'下载模板'按钮创建新的数据文件"
            )

    def download_template(self):
        """下载Excel模板"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="保存模板文件",
                defaultextension=".xlsx",
                filetypes=[("Excel文件", "*.xlsx")],
                initialfile="原料数据模板.xlsx"
            )

            if not file_path:
                return

            if self.calculator_core.raw_materials_data:
                # 使用已加载的数据
                template_data = []
                for row_data in self.calculator_core.raw_materials_data:
                    clean_row = {k: v for k, v in row_data.items() if k != '_row_index'}
                    template_data.append(clean_row)
                df = pd.DataFrame(template_data)
            else:
                # 尝试从默认文件读取
                try:
                    df = pd.read_excel('initial.xlsx')
                except FileNotFoundError:
                    messagebox.showerror(
                        "❌ 无法创建模板",
                        "没有找到原料数据！\n\n请先加载Excel数据文件，然后再下载模板。"
                    )
                    return
                except Exception as e:
                    messagebox.showerror("❌ 读取失败", f"读取 initial.xlsx 时出错:\n{str(e)}")
                    return

            # 保存模板文件
            df.to_excel(file_path, index=False)
            messagebox.showinfo("✅ 下载成功", f"模板已保存到:\n{file_path}")

        except Exception as e:
            messagebox.showerror("❌ 下载失败", f"下载模板时出错:\n{str(e)}")

    def populate_materials_table(self):
        """填充材料表格数据"""
        # 清空现有数据
        for item in self.materials_tree.get_children():
            self.materials_tree.delete(item)

        # 添加所有行数据
        for row_data in self.calculator_core.raw_materials_data:
            # 获取列值（除了内部索引）
            values = []
            for col in self.materials_tree['columns']:
                value = row_data.get(col, '')
                # 格式化显示值
                if value == '' or pd.isna(value):
                    values.append('')
                else:
                    values.append(str(value))

            # 插入行
            self.materials_tree.insert("", "end", values=values)

    def refresh_materials_table(self):
        """刷新材料表格"""
        if hasattr(self, 'materials_tree'):
            self.populate_materials_table()

    def run(self):
        """运行应用程序"""
        # 设置窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 启动主循环
        self.window.mainloop()

    def on_closing(self):
        """窗口关闭事件处理"""
        self.window.destroy()
