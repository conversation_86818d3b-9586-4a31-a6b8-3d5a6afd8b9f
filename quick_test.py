#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试选项卡文字显示问题的修复
"""

import ttkbootstrap as ttk
from ttkbootstrap import Style

def test_tab_display():
    """测试选项卡文字显示"""
    window = ttk.Window(themename="cosmo")
    window.title("选项卡文字显示测试")
    window.geometry("500x400")  # 从小窗口开始测试
    
    style = Style()
    
    # 配置样式
    style.configure(
        'Test.TNotebook',
        background='#f8f9fa',
        borderwidth=0
    )
    
    # 主容器
    main_frame = ttk.Frame(window)
    main_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    # 说明文字
    ttk.Label(
        main_frame,
        text="测试小窗口下选项卡文字显示\n请调整窗口大小观察文字是否完整显示",
        font=('Microsoft YaHei', 11),
        foreground='#495057',
        justify='center'
    ).pack(pady=(0, 10))
    
    # 信息显示
    info_label = ttk.Label(
        main_frame,
        text="窗口信息",
        font=('Consolas', 9),
        foreground='#6c757d'
    )
    info_label.pack(pady=(0, 10))
    
    # 选项卡容器
    notebook_container = ttk.Frame(main_frame)
    notebook_container.pack(fill="both", expand=True)
    notebook_container.grid_rowconfigure(0, weight=1)
    notebook_container.grid_columnconfigure(0, weight=1)
    
    # 创建选项卡
    notebook = ttk.Notebook(notebook_container, style='Test.TNotebook')
    notebook.grid(row=0, column=0, sticky="nsew")
    
    # 添加选项卡
    for name in ["单任务", "多任务", "设置"]:
        tab = ttk.Frame(notebook)
        notebook.add(tab, text=name)
        
        ttk.Label(
            tab,
            text=f"这是 {name} 选项卡",
            font=('Microsoft YaHei', 14),
            foreground='#495057'
        ).pack(expand=True)
    
    def update_tabs():
        """更新选项卡样式"""
        try:
            notebook.update_idletasks()
            notebook_container.update_idletasks()
            
            window_width = window.winfo_width()
            container_width = notebook_container.winfo_width()
            tab_count = notebook.index("end")
            
            if container_width <= 1:
                container_width = window_width - 20
            
            # 计算目标宽度
            target_tab_width = container_width // tab_count
            
            # 文本宽度估算
            text_width_estimate = 3 * 16 + 30  # 3个字符 * 16px + 边距
            
            # 根据窗口大小智能计算padding
            if container_width < 500:
                padding = max(2, min(8, target_tab_width // 12))
                mode = "超小"
            elif container_width < 800:
                padding = max(5, min(20, (target_tab_width - text_width_estimate) // 2))
                mode = "小"
            else:
                padding = max(15, min(100, (target_tab_width - text_width_estimate) // 2))
                mode = "正常"
            
            # 安全检查
            max_safe_padding = (target_tab_width - text_width_estimate) // 2
            if max_safe_padding > 0:
                padding = min(padding, max_safe_padding)
            else:
                padding = 2
            
            # 更新样式
            style.configure(
                'Test.TNotebook.Tab',
                padding=[padding, 12],
                font=('Microsoft YaHei', 12, 'bold'),
                background='#e9ecef',
                foreground='#495057',
                anchor='center'
            )
            
            style.map(
                'Test.TNotebook.Tab',
                background=[('selected', '#ffffff'), ('active', '#dee2e6')],
                foreground=[('selected', '#0d6efd'), ('active', '#495057')],
                padding=[('selected', [padding, 12]), ('!selected', [padding, 12])]
            )
            
            # 更新信息
            actual_width = text_width_estimate + padding * 2
            info_text = f"窗口: {window_width}px | 容器: {container_width}px | 模式: {mode}\n"
            info_text += f"目标宽度: {target_tab_width}px | 实际宽度: {actual_width}px | Padding: {padding}px"
            
            if actual_width > target_tab_width:
                info_text += "\n⚠️ 可能有文字遮挡"
            else:
                info_text += "\n✅ 文字显示正常"
            
            info_label.config(text=info_text)
            
            print(f"更新: 容器={container_width}px, 目标={target_tab_width}px, 实际={actual_width}px, padding={padding}px")
            
        except Exception as e:
            print(f"更新出错: {e}")
    
    def on_resize(event):
        if event.widget == window:
            window.after(50, update_tabs)
    
    # 绑定事件
    window.bind('<Configure>', on_resize)
    
    # 初始更新
    window.after(100, update_tabs)
    
    # 运行
    window.mainloop()

if __name__ == "__main__":
    print("快速测试程序启动...")
    print("请调整窗口大小，特别是缩小窗口，观察选项卡文字是否完整显示")
    test_tab_display()
