#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试小窗口下选项卡文字显示问题
"""

import ttkbootstrap as ttk
from ttkbootstrap import Style

class SmallWindowTester:
    def __init__(self):
        self.window = ttk.Window(themename="cosmo")
        self.window.title("小窗口文字显示测试")
        self.window.geometry("600x400")  # 从小窗口开始
        
        self.style = Style()
        self.setup_styles()
        self.create_interface()
        
        # 绑定事件
        self.window.bind('<Configure>', self.on_resize)
        
        # 延迟初始配置
        self.window.after(100, self.update_tab_widths)
        
    def setup_styles(self):
        """设置样式"""
        self.style.configure(
            'SmallTest.TNotebook',
            background='#f8f9fa',
            borderwidth=1
        )
        
        # 初始样式
        self.style.configure(
            'SmallTest.TNotebook.Tab',
            padding=[10, 12],
            font=('Microsoft YaHei', 12, 'bold'),
            background='#e9ecef',
            foreground='#495057',
            anchor='center'
        )
        
        self.style.map(
            'SmallTest.TNotebook.Tab',
            background=[('selected', '#ffffff'), ('active', '#dee2e6')],
            foreground=[('selected', '#0d6efd'), ('active', '#495057')]
        )
    
    def create_interface(self):
        """创建界面"""
        # 主容器
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(
            main_frame,
            text="小窗口文字显示测试",
            font=('Microsoft YaHei', 14, 'bold'),
            foreground='#0d6efd'
        )
        title_label.pack(pady=(0, 10))
        
        # 信息显示
        self.info_frame = ttk.LabelFrame(main_frame, text="调试信息", padding=5)
        self.info_frame.pack(fill="x", pady=(0, 10))
        
        self.info_text = ttk.Label(
            self.info_frame,
            text="信息加载中...",
            font=('Consolas', 9),
            foreground='#495057'
        )
        self.info_text.pack(anchor="w")
        
        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill="x", pady=(0, 10))
        
        ttk.Button(
            control_frame,
            text="超小 (400x300)",
            command=lambda: self.set_window_size(400, 300),
            bootstyle="danger"
        ).pack(side="left", padx=(0, 5))
        
        ttk.Button(
            control_frame,
            text="小 (600x400)",
            command=lambda: self.set_window_size(600, 400),
            bootstyle="warning"
        ).pack(side="left", padx=(0, 5))
        
        ttk.Button(
            control_frame,
            text="中 (800x600)",
            command=lambda: self.set_window_size(800, 600),
            bootstyle="info"
        ).pack(side="left", padx=(0, 5))
        
        ttk.Button(
            control_frame,
            text="大 (1200x800)",
            command=lambda: self.set_window_size(1200, 800),
            bootstyle="success"
        ).pack(side="left", padx=(0, 5))
        
        # 选项卡容器
        self.notebook_container = ttk.Frame(main_frame)
        self.notebook_container.pack(fill="both", expand=True)
        self.notebook_container.grid_rowconfigure(0, weight=1)
        self.notebook_container.grid_columnconfigure(0, weight=1)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(self.notebook_container, style='SmallTest.TNotebook')
        self.notebook.grid(row=0, column=0, sticky="nsew")
        
        # 添加选项卡
        for i, name in enumerate(["单任务", "多任务", "设置"]):
            tab = ttk.Frame(self.notebook)
            self.notebook.add(tab, text=name)
            
            # 添加选项卡内容
            content_label = ttk.Label(
                tab,
                text=f"{name} 选项卡\n\n检查文字是否完整显示",
                font=('Microsoft YaHei', 12),
                foreground='#495057',
                justify='center'
            )
            content_label.pack(expand=True)
    
    def set_window_size(self, width, height):
        """设置窗口大小"""
        self.window.geometry(f"{width}x{height}")
        self.window.after(200, self.update_tab_widths)
    
    def update_tab_widths(self):
        """更新选项卡宽度 - 优化小窗口显示"""
        try:
            # 确保组件已渲染
            self.notebook.update_idletasks()
            self.notebook_container.update_idletasks()
            
            # 获取尺寸信息
            window_width = self.window.winfo_width()
            container_width = self.notebook_container.winfo_width()
            tab_count = self.notebook.index("end")
            
            if container_width <= 1:
                container_width = window_width - 20
            
            # 计算目标宽度
            target_tab_width = container_width // tab_count if tab_count > 0 else 0
            
            # 计算文本宽度 - 更精确的估算
            tab_texts = ["单任务", "多任务", "设置"]
            max_text_length = max(len(text) for text in tab_texts)
            text_width_estimate = max_text_length * 16 + 30  # 每字符16px + 边距
            
            # 智能padding计算 - 针对小窗口优化
            available_space = target_tab_width - text_width_estimate
            
            if container_width < 500:  # 超小窗口
                padding_horizontal = max(2, min(8, target_tab_width // 10))
            elif container_width < 800:  # 小窗口
                padding_horizontal = max(5, min(20, available_space // 2))
            else:  # 正常窗口
                padding_horizontal = max(10, min(100, available_space // 2))
            
            # 确保padding不会让文字被遮挡
            max_safe_padding = (target_tab_width - text_width_estimate) // 2
            if max_safe_padding > 0:
                padding_horizontal = min(padding_horizontal, max_safe_padding)
            else:
                padding_horizontal = 2  # 最小padding
            
            # 更新样式
            self.style.configure(
                'SmallTest.TNotebook.Tab',
                padding=[padding_horizontal, 12],
                font=('Microsoft YaHei', 12, 'bold'),
                background='#e9ecef',
                foreground='#495057',
                anchor='center'
            )
            
            self.style.map(
                'SmallTest.TNotebook.Tab',
                background=[('selected', '#ffffff'), ('active', '#dee2e6')],
                foreground=[('selected', '#0d6efd'), ('active', '#495057')],
                padding=[('selected', [padding_horizontal, 12]), ('!selected', [padding_horizontal, 12])]
            )
            
            # 计算实际宽度
            actual_tab_width = text_width_estimate + padding_horizontal * 2
            
            # 更新信息显示
            info_text = f"""窗口: {window_width}px | 容器: {container_width}px
目标宽度: {target_tab_width}px | 文字宽度: {text_width_estimate}px
Padding: {padding_horizontal}px | 实际宽度: {actual_tab_width}px
可用空间: {available_space}px | 安全Padding: {max_safe_padding}px"""
            
            self.info_text.config(text=info_text)
            
            # 检查是否可能有文字遮挡问题
            if actual_tab_width > target_tab_width:
                print(f"⚠️  警告: 实际宽度({actual_tab_width}px) > 目标宽度({target_tab_width}px), 可能有文字遮挡")
            else:
                print(f"✅ 正常: 实际宽度({actual_tab_width}px) <= 目标宽度({target_tab_width}px)")
            
        except Exception as e:
            print(f"更新选项卡宽度时出错: {e}")
            self.info_text.config(text=f"错误: {e}")
    
    def on_resize(self, event):
        """窗口大小变化事件"""
        if event.widget == self.window:
            self.window.after(50, self.update_tab_widths)  # 更快的响应
    
    def run(self):
        """运行程序"""
        self.window.mainloop()

if __name__ == "__main__":
    app = SmallWindowTester()
    print("小窗口文字显示测试启动！")
    print("测试不同窗口大小下选项卡文字的显示情况")
    app.run()
