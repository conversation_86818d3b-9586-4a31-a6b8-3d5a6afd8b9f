#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主应用的选项卡配置
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from calculator_ui import CeramicCalculatorUI
    import time
    
    def test_main_app():
        """测试主应用"""
        print("🚀 启动主应用测试...")
        
        # 创建应用实例
        app = CeramicCalculatorUI()
        print("✅ 应用实例创建成功")
        
        # 等待一段时间让配置完成
        print("⏳ 等待配置完成...")
        
        # 手动触发配置
        print("🔧 手动触发选项卡配置...")
        app.configure_notebook_tabs()
        
        # 再等待一下
        time.sleep(1)
        
        # 尝试调整窗口大小触发重新配置
        print("📏 调整窗口大小...")
        app.window.geometry("1000x700")
        app.window.update_idletasks()
        
        # 再次手动配置
        print("🔧 再次手动配置...")
        app.configure_notebook_tabs()
        
        print("✅ 测试完成，应用应该已正确配置")
        
        # 运行应用
        print("🎯 启动应用界面...")
        app.run()
    
    if __name__ == "__main__":
        test_main_app()
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保所有依赖包已正确安装")
    
except Exception as e:
    print(f"❌ 运行错误: {e}")
    import traceback
    traceback.print_exc()
